//
//  Persistence.swift
//  Bodylog1
//
//  Created by rainkygong on 2025/7/19.
//

import CoreData
import CloudKit
import Combine

struct PersistenceController {
    static let shared = PersistenceController()

    // CloudKit容器标识符
    static let cloudKitContainerIdentifier = "iCloud.com.rainkygong.Bodylog1"

    @MainActor
    static let preview: PersistenceController = {
        let result = PersistenceController(inMemory: true)
        let viewContext = result.container.viewContext

        // 创建示例数据
        let sampleRecord = RecordEntity(context: viewContext)
        sampleRecord.id = UUID()
        sampleRecord.text = "今天头有点痛，可能是昨晚睡得太晚了"
        sampleRecord.timestamp = Date()

        let sampleUserInfo = UserInfoEntity(context: viewContext)
        sampleUserInfo.remainingCalls = 10
        sampleUserInfo.gender = "未设置"
        sampleUserInfo.birthDate = Date()

        do {
            try viewContext.save()
        } catch {
            let nsError = error as NSError
            fatalError("Unresolved error \(nsError), \(nsError.userInfo)")
        }
        return result
    }()

    let container: NSPersistentCloudKitContainer

    init(inMemory: Bool = false) {
        container = NSPersistentCloudKitContainer(name: "Bodylog1")

        if inMemory {
            container.persistentStoreDescriptions.first!.url = URL(fileURLWithPath: "/dev/null")
        } else {
            // 配置 CloudKit 同步
            guard let description = container.persistentStoreDescriptions.first else {
                fatalError("Failed to retrieve a persistent store description.")
            }

            // 启用 CloudKit 同步
            description.setOption(true as NSNumber, forKey: NSPersistentHistoryTrackingKey)
            description.setOption(true as NSNumber, forKey: NSPersistentStoreRemoteChangeNotificationPostOptionKey)

            // 配置CloudKit容器
            description.cloudKitContainerOptions = NSPersistentCloudKitContainerOptions(
                containerIdentifier: Self.cloudKitContainerIdentifier
            )

            // 设置CloudKit数据库范围为私有数据库
            description.cloudKitContainerOptions?.databaseScope = .private
        }

        container.loadPersistentStores(completionHandler: { (storeDescription, error) in
            if let error = error as NSError? {
                /*
                 典型的错误原因包括：
                 * 父目录不存在、无法创建或不允许写入
                 * 由于权限或设备锁定时的数据保护，持久存储不可访问
                 * 设备空间不足
                 * 存储无法迁移到当前模型版本
                 * CloudKit账户问题或网络连接问题
                 检查错误消息以确定实际问题
                 */
                print("Core Data error: \(error), \(error.userInfo)")

                // 在生产环境中，应该优雅地处理错误而不是崩溃
                #if DEBUG
                fatalError("Unresolved error \(error), \(error.userInfo)")
                #endif
            } else {
                print("Successfully loaded persistent store: \(storeDescription)")
            }
        })

        // 配置视图上下文
        container.viewContext.automaticallyMergesChangesFromParent = true

        // 配置合并策略以处理冲突
        container.viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy

        // 配置 CloudKit 同步策略（仅在非内存模式下）
        if !inMemory {
            do {
                try container.viewContext.setQueryGenerationFrom(.current)
            } catch {
                print("Failed to pin viewContext to the current generation: \(error)")
                // 在生产环境中不应该崩溃
                #if DEBUG
                fatalError("Failed to pin viewContext to the current generation: \(error)")
                #endif
            }

            // 监听远程变更通知
            setupRemoteChangeNotifications()
        }
    }

    // MARK: - CloudKit Remote Change Notifications

    /// 设置远程变更通知监听
    private func setupRemoteChangeNotifications() {
        NotificationCenter.default.addObserver(
            forName: .NSPersistentStoreRemoteChange,
            object: container.persistentStoreCoordinator,
            queue: .main
        ) { _ in
            print("Received remote change notification from CloudKit")
            // 这里可以添加UI更新逻辑
        }
    }
}
