# 身体日记 - 数据模型集成指南

## 🎯 集成完成状态

✅ **所有编译错误已修复**  
✅ **CoreData 实体已创建**  
✅ **CloudKit 同步已配置**  
✅ **DataViewModel 已准备就绪**  
✅ **集成示例已提供**  

## 🚀 快速开始

### 1. 更新 App 入口文件

在 `Bodylog1App.swift` 中添加 DataViewModel：

```swift
@main
struct Bodylog1App: App {
    let persistenceController = PersistenceController.shared
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.managedObjectContext, persistenceController.container.viewContext)
                .environmentObject(DataViewModel()) // 添加这行
        }
    }
}
```

### 2. 在现有 ContentView 中使用

在你的 `ContentView.swift` 中添加：

```swift
struct ContentView: View {
    @EnvironmentObject var dataViewModel: DataViewModel // 添加这行
    @State private var selectedTab = 1
    
    var body: some View {
        // 你现有的 UI 代码
        
        .onAppear {
            // 添加数据加载
            Task {
                await dataViewModel.refreshData()
            }
        }
    }
}
```

### 3. 替换示例数据

#### 在历史记录页面中：

```swift
// 替换现有的示例数据
// @State private var historyRecords: [HistoryRecord] = [...]

// 使用真实数据
var todayRecords: [RecordEntity] {
    dataViewModel.getRecords(for: selectedDate)
}

// 使用真实的记录日期
var recordDates: Set<Date> {
    dataViewModel.recordDates
}
```

#### 在记录页面中：

```swift
// 保存记录的方法
private func saveRecord() {
    let text = recordText.trimmingCharacters(in: .whitespacesAndNewlines)
    guard !text.isEmpty else { return }
    
    Task {
        await dataViewModel.addRecord(text: text, timestamp: Date())
        recordText = ""
    }
}
```

#### 在个人中心页面中：

```swift
// 显示用户信息
if let userInfo = dataViewModel.userInfo {
    Text("剩余调用次数: \(userInfo.remainingCalls)")
    Text("性别: \(userInfo.userGender?.displayName ?? "未设置")")
    Text("年龄: \(userInfo.age)岁")
}

// 显示统计信息
let stats = dataViewModel.getStatistics()
Text("总记录: \(stats.totalRecords)")
Text("报告数: \(stats.totalReports)")
Text("提醒数: \(stats.activeReminders)")
```

## 📱 核心功能使用

### 记录管理

```swift
// 添加记录
await dataViewModel.addRecord(text: "今天头痛", timestamp: Date())

// 获取指定日期的记录
let todayRecords = dataViewModel.getRecords(for: Date())

// 获取时间范围内的记录
let weekRecords = dataViewModel.getRecords(from: startDate, to: endDate)

// 删除记录
await dataViewModel.deleteRecord(record)
```

### 报告管理

```swift
// 生成分析报告
await dataViewModel.addReport(
    title: "本周健康分析",
    type: .analysis,
    content: "AI 生成的分析内容...",
    timeRange: "2025年7月14日-20日"
)

// 获取报告
let analysisReports = dataViewModel.getReports(ofType: .analysis)
let archiveReports = dataViewModel.getReports(ofType: .archive)
```

### 用户信息管理

```swift
// 消费 API 调用次数
let canCall = await dataViewModel.consumeApiCall()
if canCall {
    // 调用 AI API
} else {
    // 显示调用次数不足提示
}

// 购买后增加调用次数
await dataViewModel.addApiCalls(100)

// 更新用户信息
await dataViewModel.updateUserGender(.male)
await dataViewModel.updateUserBirthDate(birthDate)
```

### 提醒管理

```swift
// 添加提醒
await dataViewModel.addReminder(
    time: reminderTime,
    message: "记录今天的身体状态"
)

// 获取激活的提醒
let activeReminders = dataViewModel.getActiveReminders()

// 切换提醒状态
await dataViewModel.toggleReminderActive(reminder)
```

## 🔄 数据同步

### CloudKit 自动同步

- ✅ 数据自动同步到 iCloud
- ✅ 多设备数据一致性
- ✅ 离线数据访问
- ✅ 冲突自动解决

### 同步状态监控

```swift
// 监听数据变化
.onReceive(dataViewModel.$records) { records in
    // 记录数据更新
}

.onReceive(dataViewModel.$errorMessage) { errorMessage in
    if let error = errorMessage {
        // 显示错误信息
    }
}
```

## 🧪 测试验证

### 运行数据模型测试

```swift
// 在开发时验证数据模型
DataModelTests.testEntityCreation()

// 或者使用测试视图
NavigationLink("测试数据模型") {
    DataModelTestView()
}
```

### 验证 CloudKit 同步

1. 在一台设备上添加记录
2. 在另一台设备上检查数据是否同步
3. 测试离线添加数据，联网后是否同步

## ⚠️ 注意事项

### CloudKit 配置

1. **Apple Developer 配置**
   - 确保在 Apple Developer 中配置了 CloudKit 容器
   - 检查 CloudKit Schema 是否正确

2. **用户 iCloud 状态**
   - 用户需要登录 iCloud 账户
   - 检查 iCloud Drive 是否开启

3. **首次同步**
   - 首次同步可能需要一些时间
   - 大量数据同步时要有耐心

### 性能优化

1. **批量操作**
   ```swift
   // 批量删除
   CoreDataManager.batchDelete(records, in: context)
   ```

2. **分页加载**
   ```swift
   // 对于大量数据，考虑分页加载
   request.fetchLimit = 50
   request.fetchOffset = page * 50
   ```

3. **后台处理**
   ```swift
   // 大量数据操作在后台进行
   Task.detached {
       // 数据处理
   }
   ```

## 🔧 故障排除

### 常见问题

1. **数据不同步**
   - 检查网络连接
   - 确认 iCloud 登录状态
   - 重启应用

2. **调用次数不足**
   - 检查 `userInfo.remainingCalls`
   - 实现内购逻辑增加调用次数

3. **数据丢失**
   - CoreData 有自动备份
   - CloudKit 有版本历史

### 调试技巧

```swift
// 打印调试信息
print("当前记录数: \(dataViewModel.records.count)")
print("剩余调用次数: \(dataViewModel.userInfo?.remainingCalls ?? 0)")

// 检查数据有效性
if record.isValid {
    print("记录有效")
} else {
    print("记录无效: \(record)")
}
```

## 🎉 完成！

现在你的身体日记应用已经完全集成了 CoreData 数据模型，支持：

- ✅ 本地数据存储
- ✅ iCloud 自动同步
- ✅ 多设备数据一致性
- ✅ 离线使用
- ✅ 完整的 CRUD 操作
- ✅ 数据验证和错误处理

开始享受你的新数据模型吧！🚀
