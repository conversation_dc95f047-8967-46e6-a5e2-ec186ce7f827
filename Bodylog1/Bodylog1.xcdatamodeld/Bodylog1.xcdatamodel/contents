<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="22758" systemVersion="23G93" minimumToolsVersion="Automatic" sourceLanguage="Swift" usedWithCloudKit="YES" userDefinedModelVersionIdentifier="">
    <!-- 身体记录实体 -->
    <entity name="RecordEntity" representedClassName="RecordEntity" syncable="YES" codeGenerationType="class">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="text" optional="YES" attributeType="String" defaultValue=""/>
        <attribute name="timestamp" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="ckRecordID" optional="YES" attributeType="String" usesScalarValueType="NO"/>
        <attribute name="ckRecordSystemFields" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
    </entity>

    <!-- 分析报告实体 -->
    <entity name="ReportEntity" representedClassName="ReportEntity" syncable="YES" codeGenerationType="class">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="title" optional="YES" attributeType="String" defaultValue=""/>
        <attribute name="type" optional="YES" attributeType="String" defaultValue=""/>
        <attribute name="content" optional="YES" attributeType="String" defaultValue=""/>
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="timeRange" optional="YES" attributeType="String" defaultValue=""/>
        <attribute name="ckRecordID" optional="YES" attributeType="String" usesScalarValueType="NO"/>
        <attribute name="ckRecordSystemFields" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
    </entity>

    <!-- 用户信息实体 -->
    <entity name="UserInfoEntity" representedClassName="UserInfoEntity" syncable="YES" codeGenerationType="class">
        <attribute name="remainingCalls" optional="YES" attributeType="Integer 32" defaultValue="10" usesScalarValueType="YES"/>
        <attribute name="gender" optional="YES" attributeType="String" defaultValue="未设置"/>
        <attribute name="birthDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="userID" optional="YES" attributeType="String" defaultValue="default_user"/>
        <attribute name="ckRecordID" optional="YES" attributeType="String" usesScalarValueType="NO"/>
        <attribute name="ckRecordSystemFields" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
    </entity>

    <!-- 提醒实体 -->
    <entity name="ReminderEntity" representedClassName="ReminderEntity" syncable="YES" codeGenerationType="class">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="time" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="message" optional="YES" attributeType="String" defaultValue=""/>
        <attribute name="isActive" optional="YES" attributeType="Boolean" defaultValue="YES" usesScalarValueType="YES"/>
        <attribute name="ckRecordID" optional="YES" attributeType="String" usesScalarValueType="NO"/>
        <attribute name="ckRecordSystemFields" optional="YES" attributeType="Binary" allowsExternalBinaryDataStorage="YES"/>
    </entity>

    <elements>
        <element name="RecordEntity" positionX="-63" positionY="-18" width="128" height="88"/>
        <element name="ReportEntity" positionX="135" positionY="-18" width="128" height="133"/>
        <element name="UserInfoEntity" positionX="-63" positionY="135" width="128" height="88"/>
        <element name="ReminderEntity" positionX="135" positionY="135" width="128" height="103"/>
    </elements>
</model>