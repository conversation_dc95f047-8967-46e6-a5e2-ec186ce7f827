//
//  DataModels.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/20.
//

import Foundation
import CoreData
import CloudKit

// MARK: - RecordEntity 扩展
extension RecordEntity {
    
    /// 创建新的身体记录
    /// - Parameters:
    ///   - context: CoreData 管理对象上下文
    ///   - text: 记录内容
    ///   - timestamp: 记录时间，默认为当前时间
    /// - Returns: 新创建的 RecordEntity 实例
    static func create(in context: NSManagedObjectContext, 
                      text: String, 
                      timestamp: Date = Date()) -> RecordEntity {
        let record = RecordEntity(context: context)
        record.id = UUID()
        record.text = text
        record.timestamp = timestamp
        return record
    }
    
    /// 获取指定日期的所有记录
    /// - Parameters:
    ///   - date: 目标日期
    ///   - context: CoreData 管理对象上下文
    /// - Returns: 该日期的所有记录，按时间倒序排列
    static func fetchRecords(for date: Date, in context: NSManagedObjectContext) -> [RecordEntity] {
        let request: NSFetchRequest<RecordEntity> = RecordEntity.fetchRequest()
        
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: date)
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!
        
        request.predicate = NSPredicate(format: "timestamp >= %@ AND timestamp < %@", 
                                       startOfDay as NSDate, endOfDay as NSDate)
        request.sortDescriptors = [NSSortDescriptor(keyPath: \RecordEntity.timestamp, ascending: false)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("获取记录失败: \(error)")
            return []
        }
    }
    
    /// 获取指定时间范围内的所有记录
    /// - Parameters:
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    ///   - context: CoreData 管理对象上下文
    /// - Returns: 时间范围内的所有记录
    static func fetchRecords(from startDate: Date, 
                           to endDate: Date, 
                           in context: NSManagedObjectContext) -> [RecordEntity] {
        let request: NSFetchRequest<RecordEntity> = RecordEntity.fetchRequest()
        request.predicate = NSPredicate(format: "timestamp >= %@ AND timestamp <= %@", 
                                       startDate as NSDate, endDate as NSDate)
        request.sortDescriptors = [NSSortDescriptor(keyPath: \RecordEntity.timestamp, ascending: true)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("获取时间范围记录失败: \(error)")
            return []
        }
    }
    
    /// 获取有记录的日期集合
    /// - Parameter context: CoreData 管理对象上下文
    /// - Returns: 包含记录的日期集合
    static func fetchRecordDates(in context: NSManagedObjectContext) -> Set<Date> {
        let request: NSFetchRequest<RecordEntity> = RecordEntity.fetchRequest()
        request.sortDescriptors = [NSSortDescriptor(keyPath: \RecordEntity.timestamp, ascending: false)]
        
        do {
            let records = try context.fetch(request)
            let calendar = Calendar.current
            return Set(records.map { calendar.startOfDay(for: $0.timestamp!) })
        } catch {
            print("获取记录日期失败: \(error)")
            return Set()
        }
    }
    
    /// 格式化显示时间
    var formattedTime: String {
        guard let timestamp = timestamp else { return "" }
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: timestamp)
    }
    
    /// 格式化显示日期
    var formattedDate: String {
        guard let timestamp = timestamp else { return "" }
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: timestamp)
    }
}

// MARK: - ReportEntity 扩展
extension ReportEntity {
    
    /// 报告类型枚举
    enum ReportType: String, CaseIterable {
        case analysis = "分析报告"
        case archive = "身体档案"
        
        var displayName: String {
            return self.rawValue
        }
    }
    
    /// 创建新的分析报告
    /// - Parameters:
    ///   - context: CoreData 管理对象上下文
    ///   - title: 报告标题
    ///   - type: 报告类型
    ///   - content: 报告内容
    ///   - timeRange: 时间范围描述
    /// - Returns: 新创建的 ReportEntity 实例
    static func create(in context: NSManagedObjectContext,
                      title: String,
                      type: ReportType,
                      content: String,
                      timeRange: String) -> ReportEntity {
        let report = ReportEntity(context: context)
        report.id = UUID()
        report.title = title
        report.type = type.rawValue
        report.content = content
        report.createdAt = Date()
        report.timeRange = timeRange
        return report
    }
    
    /// 获取指定类型的所有报告
    /// - Parameters:
    ///   - type: 报告类型
    ///   - context: CoreData 管理对象上下文
    /// - Returns: 指定类型的所有报告，按创建时间倒序排列
    static func fetchReports(ofType type: ReportType, 
                           in context: NSManagedObjectContext) -> [ReportEntity] {
        let request: NSFetchRequest<ReportEntity> = ReportEntity.fetchRequest()
        request.predicate = NSPredicate(format: "type == %@", type.rawValue)
        request.sortDescriptors = [NSSortDescriptor(keyPath: \ReportEntity.createdAt, ascending: false)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("获取报告失败: \(error)")
            return []
        }
    }
    
    /// 获取所有报告
    /// - Parameter context: CoreData 管理对象上下文
    /// - Returns: 所有报告，按创建时间倒序排列
    static func fetchAllReports(in context: NSManagedObjectContext) -> [ReportEntity] {
        let request: NSFetchRequest<ReportEntity> = ReportEntity.fetchRequest()
        request.sortDescriptors = [NSSortDescriptor(keyPath: \ReportEntity.createdAt, ascending: false)]
        
        do {
            return try context.fetch(request)
        } catch {
            print("获取所有报告失败: \(error)")
            return []
        }
    }
    
    /// 报告类型的枚举值
    var reportType: ReportType? {
        guard let type = type else { return nil }
        return ReportType(rawValue: type)
    }
    
    /// 格式化创建时间
    var formattedCreatedAt: String {
        guard let createdAt = createdAt else { return "" }
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: createdAt)
    }
}

// MARK: - UserInfoEntity 扩展
extension UserInfoEntity {
    
    /// 性别枚举
    enum Gender: String, CaseIterable {
        case male = "男"
        case female = "女"
        case notSet = "未设置"
        
        var displayName: String {
            return self.rawValue
        }
    }
    
    /// 获取或创建用户信息（单例模式）
    /// - Parameter context: CoreData 管理对象上下文
    /// - Returns: 用户信息实例
    static func getOrCreate(in context: NSManagedObjectContext) -> UserInfoEntity {
        let request: NSFetchRequest<UserInfoEntity> = UserInfoEntity.fetchRequest()
        
        do {
            let userInfos = try context.fetch(request)
            if let userInfo = userInfos.first {
                return userInfo
            } else {
                // 创建新的用户信息
                let userInfo = UserInfoEntity(context: context)
                userInfo.remainingCalls = 10 // 首次注册赠送10次调用
                userInfo.gender = Gender.notSet.rawValue
                userInfo.birthDate = Date()
                return userInfo
            }
        } catch {
            print("获取用户信息失败: \(error)")
            // 创建新的用户信息作为备选
            let userInfo = UserInfoEntity(context: context)
            userInfo.remainingCalls = 10
            userInfo.gender = Gender.notSet.rawValue
            userInfo.birthDate = Date()
            return userInfo
        }
    }
    
    /// 消费调用次数
    /// - Parameter count: 消费的次数，默认为1
    /// - Returns: 是否成功消费（剩余次数足够）
    @discardableResult
    func consumeCalls(_ count: Int32 = 1) -> Bool {
        if remainingCalls >= count {
            remainingCalls -= count
            return true
        }
        return false
    }
    
    /// 增加调用次数（内购后）
    /// - Parameter count: 增加的次数
    func addCalls(_ count: Int32) {
        remainingCalls += count
    }
    
    /// 用户性别的枚举值
    var userGender: Gender? {
        guard let gender = gender else { return nil }
        return Gender(rawValue: gender)
    }
    
    /// 计算年龄
    var age: Int {
        guard let birthDate = birthDate else { return 0 }
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.year], from: birthDate, to: Date())
        return ageComponents.year ?? 0
    }
    
    /// 格式化出生日期
    var formattedBirthDate: String {
        guard let birthDate = birthDate else { return "" }
        let formatter = DateFormatter()
        formatter.dateStyle = .long
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: birthDate)
    }
}

// MARK: - ReminderEntity 扩展
extension ReminderEntity {

    /// 创建新的提醒
    /// - Parameters:
    ///   - context: CoreData 管理对象上下文
    ///   - time: 提醒时间
    ///   - message: 提醒消息
    ///   - isActive: 是否激活，默认为true
    /// - Returns: 新创建的 ReminderEntity 实例
    static func create(in context: NSManagedObjectContext,
                      time: Date,
                      message: String,
                      isActive: Bool = true) -> ReminderEntity {
        let reminder = ReminderEntity(context: context)
        reminder.id = UUID()
        reminder.time = time
        reminder.message = message
        reminder.isActive = isActive
        return reminder
    }

    /// 获取所有激活的提醒
    /// - Parameter context: CoreData 管理对象上下文
    /// - Returns: 所有激活的提醒，按时间排序
    static func fetchActiveReminders(in context: NSManagedObjectContext) -> [ReminderEntity] {
        let request: NSFetchRequest<ReminderEntity> = ReminderEntity.fetchRequest()
        request.predicate = NSPredicate(format: "isActive == YES")
        request.sortDescriptors = [NSSortDescriptor(keyPath: \ReminderEntity.time, ascending: true)]

        do {
            return try context.fetch(request)
        } catch {
            print("获取激活提醒失败: \(error)")
            return []
        }
    }

    /// 获取所有提醒
    /// - Parameter context: CoreData 管理对象上下文
    /// - Returns: 所有提醒，按时间排序
    static func fetchAllReminders(in context: NSManagedObjectContext) -> [ReminderEntity] {
        let request: NSFetchRequest<ReminderEntity> = ReminderEntity.fetchRequest()
        request.sortDescriptors = [NSSortDescriptor(keyPath: \ReminderEntity.time, ascending: true)]

        do {
            return try context.fetch(request)
        } catch {
            print("获取所有提醒失败: \(error)")
            return []
        }
    }

    /// 切换提醒激活状态
    func toggleActive() {
        isActive.toggle()
    }

    /// 格式化提醒时间
    var formattedTime: String {
        guard let time = time else { return "" }
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: time)
    }

    /// 获取提醒的小时和分钟
    var timeComponents: (hour: Int, minute: Int) {
        guard let time = time else { return (0, 0) }
        let calendar = Calendar.current
        let components = calendar.dateComponents([.hour, .minute], from: time)
        return (components.hour ?? 0, components.minute ?? 0)
    }

    /// 生成通知标识符
    var notificationIdentifier: String {
        return "reminder_\(id?.uuidString ?? UUID().uuidString)"
    }
}

// MARK: - CoreData 辅助工具
class CoreDataManager {

    /// 保存上下文
    /// - Parameter context: 要保存的上下文
    static func save(context: NSManagedObjectContext) {
        if context.hasChanges {
            do {
                try context.save()
            } catch {
                print("保存上下文失败: \(error)")
                let nsError = error as NSError
                fatalError("Unresolved error \(nsError), \(nsError.userInfo)")
            }
        }
    }

    /// 删除对象
    /// - Parameters:
    ///   - object: 要删除的对象
    ///   - context: CoreData 管理对象上下文
    static func delete<T: NSManagedObject>(_ object: T, in context: NSManagedObjectContext) {
        context.delete(object)
        save(context: context)
    }

    /// 批量删除
    /// - Parameters:
    ///   - objects: 要删除的对象数组
    ///   - context: CoreData 管理对象上下文
    static func batchDelete<T: NSManagedObject>(_ objects: [T], in context: NSManagedObjectContext) {
        objects.forEach { context.delete($0) }
        save(context: context)
    }
}

// MARK: - 数据验证扩展
extension RecordEntity {

    /// 验证记录数据的有效性
    var isValid: Bool {
        guard let text = text, !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return false
        }
        guard timestamp != nil else {
            return false
        }
        return true
    }
}

extension ReportEntity {

    /// 验证报告数据的有效性
    var isValid: Bool {
        guard let title = title, !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return false
        }
        guard let type = type, !type.isEmpty else {
            return false
        }
        guard let content = content, !content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return false
        }
        guard let timeRange = timeRange, !timeRange.isEmpty else {
            return false
        }
        return true
    }
}

extension ReminderEntity {

    /// 验证提醒数据的有效性
    var isValid: Bool {
        guard let message = message, !message.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return false
        }
        guard time != nil else {
            return false
        }
        return true
    }
}
