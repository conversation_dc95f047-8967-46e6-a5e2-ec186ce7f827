# 身体日记 CoreData 数据模型

## 概述

本项目使用 CoreData + CloudKit 进行数据存储和同步，支持 iOS 16.6+ 系统。数据模型包含四个主要实体，用于管理用户的身体记录、分析报告、用户信息和提醒设置。

## 数据实体

### 1. RecordEntity (身体记录实体)
用于存储用户的身体状态记录。

**属性：**
- `id: UUID` - 唯一标识符
- `text: String` - 记录内容
- `timestamp: Date` - 记录时间

**主要方法：**
```swift
// 创建新记录
let record = RecordEntity.create(in: context, text: "今天头痛", timestamp: Date())

// 获取指定日期的记录
let todayRecords = RecordEntity.fetchRecords(for: Date(), in: context)

// 获取时间范围内的记录
let weekRecords = RecordEntity.fetchRecords(from: startDate, to: endDate, in: context)

// 获取有记录的日期集合
let recordDates = RecordEntity.fetchRecordDates(in: context)
```

### 2. ReportEntity (分析报告实体)
用于存储 AI 生成的分析报告和身体档案。

**属性：**
- `id: UUID` - 唯一标识符
- `title: String` - 报告标题
- `type: String` - 报告类型（"分析报告" 或 "身体档案"）
- `content: String` - 报告内容
- `createdAt: Date` - 创建时间
- `timeRange: String` - 时间范围描述

**主要方法：**
```swift
// 创建分析报告
let report = ReportEntity.create(
    in: context,
    title: "本周健康分析",
    type: .analysis,
    content: "分析内容...",
    timeRange: "2025年7月14日-20日"
)

// 获取指定类型的报告
let analysisReports = ReportEntity.fetchReports(ofType: .analysis, in: context)
let archiveReports = ReportEntity.fetchReports(ofType: .archive, in: context)
```

### 3. UserInfoEntity (用户信息实体)
用于存储用户的基本信息和 API 调用次数。

**属性：**
- `remainingCalls: Int32` - 剩余 API 调用次数（默认 20）
- `gender: String?` - 性别（"男"/"女"/"未设置"）
- `birthDate: Date?` - 出生日期

**主要方法：**
```swift
// 获取或创建用户信息（单例模式）
let userInfo = UserInfoEntity.getOrCreate(in: context)

// 消费调用次数
let success = userInfo.consumeCalls(1)

// 增加调用次数（内购后）
userInfo.addCalls(100)

// 获取年龄
let age = userInfo.age
```

### 4. ReminderEntity (提醒实体)
用于存储用户设置的提醒。

**属性：**
- `id: UUID` - 唯一标识符
- `time: Date` - 提醒时间
- `message: String` - 提醒消息
- `isActive: Bool` - 是否激活（默认 true）

**主要方法：**
```swift
// 创建提醒
let reminder = ReminderEntity.create(
    in: context,
    time: reminderTime,
    message: "记录今天的身体状态"
)

// 获取激活的提醒
let activeReminders = ReminderEntity.fetchActiveReminders(in: context)

// 切换激活状态
reminder.toggleActive()
```

## 使用方式

### 1. 基本设置

在 `Bodylog1App.swift` 中：
```swift
@main
struct Bodylog1App: App {
    let persistenceController = PersistenceController.shared
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.managedObjectContext, persistenceController.container.viewContext)
                .environmentObject(DataViewModel())
        }
    }
}
```

### 2. 在视图中使用 DataViewModel

```swift
struct ContentView: View {
    @EnvironmentObject var dataViewModel: DataViewModel
    
    var body: some View {
        VStack {
            // 显示记录
            ForEach(dataViewModel.records, id: \.objectID) { record in
                Text(record.text ?? "")
            }
            
            // 添加记录按钮
            Button("添加记录") {
                Task {
                    await dataViewModel.addRecord(text: "新记录", timestamp: Date())
                }
            }
        }
        .onAppear {
            Task {
                await dataViewModel.refreshData()
            }
        }
    }
}
```

### 3. 常用操作示例

```swift
// 添加身体记录
await dataViewModel.addRecord(text: "今天头痛，可能是睡眠不足", timestamp: Date())

// 获取今天的记录
let todayRecords = dataViewModel.getRecords(for: Date())

// 添加分析报告
await dataViewModel.addReport(
    title: "本周健康分析",
    type: .analysis,
    content: "根据本周记录分析...",
    timeRange: "2025年7月14日-20日"
)

// 设置提醒
await dataViewModel.addReminder(
    time: reminderTime,
    message: "记录身体状态"
)

// 消费 API 调用
let canCall = await dataViewModel.consumeApiCall()
if canCall {
    // 调用 AI API
}

// 更新用户信息
await dataViewModel.updateUserGender(.male)
await dataViewModel.updateUserBirthDate(birthDate)
```

## CloudKit 同步

项目已配置 CloudKit 同步，支持：
- 自动同步到 iCloud
- 多设备数据一致性
- 离线数据访问
- 冲突解决

**注意事项：**
1. 用户需要登录 iCloud 账户
2. 应用需要在 Apple Developer 中配置 CloudKit
3. 首次同步可能需要一些时间

## 数据验证

每个实体都包含数据验证方法：
```swift
// 验证记录有效性
if record.isValid {
    // 记录有效
}

// 验证报告有效性
if report.isValid {
    // 报告有效
}
```

## 错误处理

使用 `DataViewModel` 的错误处理：
```swift
if let errorMessage = dataViewModel.errorMessage {
    // 显示错误信息
    Text(errorMessage)
        .foregroundColor(.red)
}
```

## 性能优化

1. 使用 `@FetchRequest` 进行实时数据绑定
2. 批量操作使用 `CoreDataManager.batchDelete`
3. 异步操作避免阻塞主线程
4. 适当使用 `NSFetchedResultsController` 处理大量数据

## 迁移和版本管理

当需要修改数据模型时：
1. 创建新的数据模型版本
2. 设置迁移策略
3. 测试数据迁移
4. 更新 CloudKit Schema
