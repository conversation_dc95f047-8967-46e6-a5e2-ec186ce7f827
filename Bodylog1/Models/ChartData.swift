//
//  ChartData.swift
//  Bodylog1
//
//  Created by rainkygong on 2025/7/20.
//

import Foundation

// MARK: - 图表数据模型

/// 症状趋势数据点
struct SymptomTrendData: Identifiable {
    let id = UUID()
    let date: Date
    let symptom: String
    let count: Int
}

/// 饮食与不适关联数据
struct FoodDiscomfortData: Identifiable {
    let id = UUID()
    let date: Date
    let foodEvent: String
    let discomfortEvent: String?
    let hasDiscomfort: Bool
}

/// 作息与症状关联数据
struct SleepSymptomData: Identifiable {
    let id = UUID()
    let date: Date
    let sleepQuality: String // "正常", "熬夜", "失眠"
    let symptomCount: Int
}

/// 症状频率统计
struct SymptomFrequency: Identifiable {
    let id: UUID
    let symptom: String
    let count: Int
    let percentage: Double
    let trend: String // "增加", "减少", "稳定"

    init(symptom: String, count: Int, percentage: Double, trend: String = "稳定") {
        self.id = UUID()
        self.symptom = symptom
        self.count = count
        self.percentage = percentage
        self.trend = trend
    }
}

// MARK: - 图表数据处理器

class ChartDataProcessor {
    
    /// 从记录中提取症状趋势数据
    static func extractSymptomTrends(from records: [RecordEntity]) -> [SymptomTrendData] {
        let calendar = Calendar.current
        var symptomCounts: [String: [Date: Int]] = [:]

        // 扩展症状关键词，包含更多可能的表达
        let symptoms = [
            "头痛", "头疼", "头晕", "头昏",
            "胃痛", "胃疼", "胃不舒服", "胃胀",
            "腹痛", "腹疼", "肚子痛", "肚子疼",
            "恶心", "想吐", "反胃",
            "疲劳", "累", "疲惫", "乏力",
            "失眠", "睡不着", "睡眠不好",
            "心悸", "心跳快", "心慌",
            "胸闷", "胸痛", "胸疼",
            "咳嗽", "咳", "发热", "发烧", "烧",
            "痛", "疼", "不舒服", "难受"
        ]

        for record in records {
            guard let text = record.text,
                  let timestamp = record.timestamp else { continue }

            let dayStart = calendar.startOfDay(for: timestamp)

            for symptom in symptoms {
                if text.contains(symptom) {
                    if symptomCounts[symptom] == nil {
                        symptomCounts[symptom] = [:]
                    }
                    symptomCounts[symptom]![dayStart, default: 0] += 1
                }
            }
        }

        var trendData: [SymptomTrendData] = []
        for (symptom, dateCounts) in symptomCounts {
            for (date, count) in dateCounts {
                trendData.append(SymptomTrendData(date: date, symptom: symptom, count: count))
            }
        }

        return trendData.sorted { $0.date < $1.date }
    }
    
    /// 从记录中提取饮食与不适关联数据
    static func extractFoodDiscomfortData(from records: [RecordEntity]) -> [FoodDiscomfortData] {
        let calendar = Calendar.current
        var dailyData: [Date: (foods: [String], discomforts: [String])] = [:]
        
        // 饮食关键词
        let foodKeywords = ["牛奶", "螃蟹", "海鲜", "辛辣", "油腻", "甜食", "咖啡", "酒", "冷饮"]
        // 不适关键词
        let discomfortKeywords = ["腹痛", "腹泻", "恶心", "呕吐", "胃痛", "消化不良", "过敏"]
        
        for record in records {
            guard let text = record.text?.lowercased(),
                  let timestamp = record.timestamp else { continue }
            
            let dayStart = calendar.startOfDay(for: timestamp)
            
            if dailyData[dayStart] == nil {
                dailyData[dayStart] = (foods: [], discomforts: [])
            }
            
            // 检查饮食
            for food in foodKeywords {
                if text.contains(food) {
                    dailyData[dayStart]!.foods.append(food)
                }
            }
            
            // 检查不适
            for discomfort in discomfortKeywords {
                if text.contains(discomfort) {
                    dailyData[dayStart]!.discomforts.append(discomfort)
                }
            }
        }
        
        var foodDiscomfortData: [FoodDiscomfortData] = []
        for (date, data) in dailyData {
            if !data.foods.isEmpty {
                let hasDiscomfort = !data.discomforts.isEmpty
                let foodEvent = data.foods.joined(separator: ", ")
                let discomfortEvent = data.discomforts.isEmpty ? nil : data.discomforts.joined(separator: ", ")
                
                foodDiscomfortData.append(FoodDiscomfortData(
                    date: date,
                    foodEvent: foodEvent,
                    discomfortEvent: discomfortEvent,
                    hasDiscomfort: hasDiscomfort
                ))
            }
        }
        
        return foodDiscomfortData.sorted { $0.date < $1.date }
    }
    
    /// 从记录中提取作息与症状关联数据
    static func extractSleepSymptomData(from records: [RecordEntity]) -> [SleepSymptomData] {
        let calendar = Calendar.current
        var dailyData: [Date: (sleepQuality: String, symptomCount: Int)] = [:]
        
        // 作息关键词
        let sleepKeywords = ["熬夜": "熬夜", "失眠": "失眠", "晚睡": "熬夜", "睡眠不足": "熬夜"]
        // 症状关键词
        let symptomKeywords = ["头痛", "头晕", "疲劳", "乏力", "精神不振", "注意力不集中"]
        
        for record in records {
            guard let text = record.text?.lowercased(),
                  let timestamp = record.timestamp else { continue }
            
            let dayStart = calendar.startOfDay(for: timestamp)
            
            if dailyData[dayStart] == nil {
                dailyData[dayStart] = (sleepQuality: "正常", symptomCount: 0)
            }
            
            // 检查作息
            for (keyword, quality) in sleepKeywords {
                if text.contains(keyword) {
                    dailyData[dayStart]!.sleepQuality = quality
                }
            }
            
            // 检查症状
            for symptom in symptomKeywords {
                if text.contains(symptom) {
                    dailyData[dayStart]!.symptomCount += 1
                }
            }
        }
        
        var sleepSymptomData: [SleepSymptomData] = []
        for (date, data) in dailyData {
            sleepSymptomData.append(SleepSymptomData(
                date: date,
                sleepQuality: data.sleepQuality,
                symptomCount: data.symptomCount
            ))
        }
        
        return sleepSymptomData.sorted { $0.date < $1.date }
    }
    
    /// 计算症状频率统计
    static func calculateSymptomFrequency(from records: [RecordEntity]) -> [SymptomFrequency] {
        let symptoms = [
            "头痛", "头疼", "头晕", "头昏",
            "胃痛", "胃疼", "胃不舒服", "胃胀",
            "腹痛", "腹疼", "肚子痛", "肚子疼",
            "恶心", "想吐", "反胃",
            "疲劳", "累", "疲惫", "乏力",
            "失眠", "睡不着", "睡眠不好",
            "心悸", "心跳快", "心慌",
            "胸闷", "胸痛", "胸疼",
            "咳嗽", "咳", "发热", "发烧", "烧",
            "痛", "疼", "不舒服", "难受"
        ]
        var symptomCounts: [String: Int] = [:]

        for record in records {
            guard let text = record.text else { continue }

            for symptom in symptoms {
                if text.contains(symptom) {
                    symptomCounts[symptom, default: 0] += 1
                }
            }
        }
        
        let totalSymptoms = symptomCounts.values.reduce(0, +)
        guard totalSymptoms > 0 else { return [] }
        
        var frequencies: [SymptomFrequency] = []
        for (symptom, count) in symptomCounts {
            if count > 0 {
                let percentage = Double(count) / Double(totalSymptoms) * 100
                frequencies.append(SymptomFrequency(
                    symptom: symptom,
                    count: count,
                    percentage: percentage,
                    trend: "稳定"
                ))
            }
        }
        
        return frequencies.sorted { $0.count > $1.count }
    }

    /// 分析饮食与不适关联
    static func analyzeFoodDiscomfortCorrelation(from records: [RecordEntity]) -> [FoodDiscomfortData] {
        var foodDiscomfortData: [FoodDiscomfortData] = []
        let calendar = Calendar.current
        var dailyData: [Date: (foods: Set<String>, discomforts: Set<String>)] = [:]

        // 扩展饮食关键词
        let foodKeywords = ["牛奶", "螃蟹", "虾", "鸡蛋", "坚果", "辣椒", "咖啡", "酒", "海鲜", "辛辣", "油腻", "甜食", "冷饮", "豆制品"]
        // 扩展不适关键词
        let discomfortKeywords = ["不舒服", "难受", "痛", "疼", "胀", "恶心", "腹痛", "腹泻", "呕吐", "胃痛", "消化不良", "过敏", "头痛"]

        // 第一步：收集每日的饮食和不适记录
        for record in records {
            guard let text = record.text,
                  let timestamp = record.timestamp else { continue }

            let dayStart = calendar.startOfDay(for: timestamp)

            if dailyData[dayStart] == nil {
                dailyData[dayStart] = (foods: Set<String>(), discomforts: Set<String>())
            }

            // 检查饮食关键词
            for foodKeyword in foodKeywords {
                if text.contains(foodKeyword) {
                    dailyData[dayStart]!.foods.insert(foodKeyword)
                }
            }

            // 检查不适关键词
            for discomfortKeyword in discomfortKeywords {
                if text.contains(discomfortKeyword) {
                    dailyData[dayStart]!.discomforts.insert(discomfortKeyword)
                }
            }
        }

        // 第二步：生成关联数据（包括有饮食但无不适的情况）
        for (date, data) in dailyData {
            if !data.foods.isEmpty {
                let hasDiscomfort = !data.discomforts.isEmpty
                let foodEvent = Array(data.foods).joined(separator: ", ")
                let discomfortEvent = data.discomforts.isEmpty ? nil : Array(data.discomforts).joined(separator: ", ")

                foodDiscomfortData.append(FoodDiscomfortData(
                    date: date,
                    foodEvent: foodEvent,
                    discomfortEvent: discomfortEvent,
                    hasDiscomfort: hasDiscomfort
                ))
            }
        }

        return foodDiscomfortData.sorted { $0.date < $1.date }
    }

    /// 分析作息与症状关联
    static func analyzeSleepSymptomCorrelation(from records: [RecordEntity]) -> [SleepSymptomData] {
        var sleepSymptomData: [SleepSymptomData] = []
        let calendar = Calendar.current
        var dailyData: [Date: (sleepQuality: String, symptomCount: Int)] = [:]

        // 扩展作息关键词
        let sleepQualityKeywords = [
            "熬夜": "熬夜",
            "失眠": "失眠",
            "睡不着": "失眠",
            "晚睡": "熬夜",
            "睡眠不足": "熬夜",
            "早睡": "正常",
            "睡得好": "正常",
            "充足睡眠": "正常",
            "规律作息": "正常"
        ]

        // 扩展症状关键词
        let symptoms = ["头痛", "疲劳", "头晕", "胃痛", "心悸", "乏力", "精神不振", "注意力不集中", "眼部不适", "腰痛"]

        // 第一步：收集每日的作息和症状数据
        for record in records {
            guard let text = record.text,
                  let timestamp = record.timestamp else { continue }

            let dayStart = calendar.startOfDay(for: timestamp)

            if dailyData[dayStart] == nil {
                dailyData[dayStart] = (sleepQuality: "正常", symptomCount: 0)
            }

            // 检查作息关键词（优先级：差 > 正常）
            for (sleepKeyword, quality) in sleepQualityKeywords {
                if text.contains(sleepKeyword) {
                    if quality != "正常" || dailyData[dayStart]!.sleepQuality == "正常" {
                        dailyData[dayStart]!.sleepQuality = quality
                    }
                }
            }

            // 检查症状关键词
            for symptom in symptoms {
                if text.contains(symptom) {
                    dailyData[dayStart]!.symptomCount += 1
                }
            }
        }

        // 第二步：生成关联数据（包括有作息记录的所有情况）
        for (date, data) in dailyData {
            sleepSymptomData.append(SleepSymptomData(
                date: date,
                sleepQuality: data.sleepQuality,
                symptomCount: data.symptomCount
            ))
        }

        return sleepSymptomData.sorted { $0.date < $1.date }
    }
}
