//
//  DataViewModel.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/20.
//

import Foundation
import CoreData
import SwiftUI
import Combine

/// 数据管理 ViewModel
@MainActor
class DataViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var records: [RecordEntity] = []
    @Published var reports: [ReportEntity] = []
    @Published var reminders: [ReminderEntity] = []
    @Published var userInfo: UserInfoEntity?
    @Published var recordDates: Set<Date> = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private let context: NSManagedObjectContext
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init(context: NSManagedObjectContext = PersistenceController.shared.container.viewContext) {
        self.context = context
        setupNotifications()
        loadInitialData()
    }

    // 预览专用初始化方法
    static func preview() -> DataViewModel {
        let viewModel = DataViewModel(context: PersistenceController.preview.container.viewContext)
        return viewModel
    }
    
    // MARK: - Setup
    private func setupNotifications() {
        // 监听 CoreData 变化
        NotificationCenter.default.publisher(for: .NSManagedObjectContextDidSave)
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.refreshData()
                }
            }
            .store(in: &cancellables)
    }
    
    private func loadInitialData() {
        Task {
            await refreshData()
            await loadUserInfo()
        }
    }
    
    // MARK: - Data Loading
    func refreshData() async {
        print("🔍 [DataViewModel] 开始刷新数据")
        isLoading = true
        defer {
            isLoading = false
            print("🔍 [DataViewModel] 数据刷新完成")
        }

        do {
            // 加载记录
            records = try await loadRecords()
            print("🔍 [DataViewModel] 加载记录完成 - 数量: \(records.count)")

            // 加载报告
            reports = try await loadReports()
            print("🔍 [DataViewModel] 加载报告完成 - 数量: \(reports.count)")

            // 加载提醒
            reminders = try await loadReminders()
            print("🔍 [DataViewModel] 加载提醒完成 - 数量: \(reminders.count)")

            // 加载记录日期
            recordDates = await loadRecordDates()
            print("🔍 [DataViewModel] 加载记录日期完成 - 数量: \(recordDates.count)")

        } catch {
            errorMessage = "数据加载失败: \(error.localizedDescription)"
            print("❌ [DataViewModel] 数据加载失败: \(error.localizedDescription)")
        }
    }
    
    private func loadRecords() async throws -> [RecordEntity] {
        return await withCheckedContinuation { continuation in
            context.perform {
                let request: NSFetchRequest<RecordEntity> = RecordEntity.fetchRequest()
                request.sortDescriptors = [NSSortDescriptor(keyPath: \RecordEntity.timestamp, ascending: false)]
                
                do {
                    let records = try self.context.fetch(request)
                    continuation.resume(returning: records)
                } catch {
                    continuation.resume(returning: [])
                }
            }
        }
    }
    
    private func loadReports() async throws -> [ReportEntity] {
        return await withCheckedContinuation { continuation in
            context.perform {
                let request: NSFetchRequest<ReportEntity> = ReportEntity.fetchRequest()
                request.sortDescriptors = [NSSortDescriptor(keyPath: \ReportEntity.createdAt, ascending: false)]
                
                do {
                    let reports = try self.context.fetch(request)
                    continuation.resume(returning: reports)
                } catch {
                    continuation.resume(returning: [])
                }
            }
        }
    }
    
    private func loadReminders() async throws -> [ReminderEntity] {
        return await withCheckedContinuation { continuation in
            context.perform {
                let request: NSFetchRequest<ReminderEntity> = ReminderEntity.fetchRequest()
                request.sortDescriptors = [NSSortDescriptor(keyPath: \ReminderEntity.time, ascending: true)]
                
                do {
                    let reminders = try self.context.fetch(request)
                    continuation.resume(returning: reminders)
                } catch {
                    continuation.resume(returning: [])
                }
            }
        }
    }
    
    private func loadRecordDates() async -> Set<Date> {
        return await withCheckedContinuation { continuation in
            context.perform {
                let calendar = Calendar.current
                let dates = Set(self.records.compactMap { record -> Date? in
                    guard let timestamp = record.timestamp else { return nil }
                    return calendar.startOfDay(for: timestamp)
                })
                continuation.resume(returning: dates)
            }
        }
    }
    
    private func loadUserInfo() async {
        await withCheckedContinuation { continuation in
            context.perform {
                self.userInfo = UserInfoEntity.getOrCreate(in: self.context)
                continuation.resume()
            }
        }
    }
    
    // MARK: - Record Operations
    func addRecord(text: String, timestamp: Date = Date()) async throws {
        await withCheckedContinuation { continuation in
            context.perform {
                let _ = RecordEntity.create(in: self.context, text: text, timestamp: timestamp)
                CoreDataManager.save(context: self.context)
                continuation.resume()
            }
        }
        await refreshData()
    }
    
    func deleteRecord(_ record: RecordEntity) async {
        await withCheckedContinuation { continuation in
            context.perform {
                CoreDataManager.delete(record, in: self.context)
                continuation.resume()
            }
        }
        await refreshData()
    }
    
    func getRecords(for date: Date) -> [RecordEntity] {
        return RecordEntity.fetchRecords(for: date, in: context)
    }
    
    func getRecords(from startDate: Date, to endDate: Date) -> [RecordEntity] {
        return RecordEntity.fetchRecords(from: startDate, to: endDate, in: context)
    }
    
    // MARK: - Report Operations
    func addReport(title: String, type: ReportEntity.ReportType, content: String, timeRange: String) async {
        await withCheckedContinuation { continuation in
            context.perform {
                let _ = ReportEntity.create(in: self.context,
                                               title: title,
                                               type: type,
                                               content: content,
                                               timeRange: timeRange)
                CoreDataManager.save(context: self.context)
                continuation.resume()
            }
        }
        await refreshData()
    }
    
    func deleteReport(_ report: ReportEntity) async {
        await withCheckedContinuation { continuation in
            context.perform {
                CoreDataManager.delete(report, in: self.context)
                continuation.resume()
            }
        }
        await refreshData()
    }
    
    func getReports(ofType type: ReportEntity.ReportType) -> [ReportEntity] {
        let reports = ReportEntity.fetchReports(ofType: type, in: context)
        print("🔍 [DataViewModel] 获取报告 - 类型: \(type.rawValue), 数量: \(reports.count)")

        for (index, report) in reports.enumerated() {
            print("🔍 [DataViewModel] 报告\(index): 标题='\(report.title ?? "无标题")', 内容长度=\(report.content?.count ?? 0)")
        }

        return reports
    }
    
    // MARK: - Reminder Operations
    func addReminder(time: Date, message: String, isActive: Bool = true) async {
        await withCheckedContinuation { continuation in
            context.perform {
                let _ = ReminderEntity.create(in: self.context,
                                                   time: time,
                                                   message: message,
                                                   isActive: isActive)
                CoreDataManager.save(context: self.context)
                continuation.resume()
            }
        }
        await refreshData()
    }
    
    func deleteReminder(_ reminder: ReminderEntity) async {
        await withCheckedContinuation { continuation in
            context.perform {
                CoreDataManager.delete(reminder, in: self.context)
                continuation.resume()
            }
        }
        await refreshData()
    }
    
    func toggleReminderActive(_ reminder: ReminderEntity) async {
        await withCheckedContinuation { continuation in
            context.perform {
                reminder.toggleActive()
                CoreDataManager.save(context: self.context)
                continuation.resume()
            }
        }
        await refreshData()
    }
    
    func getActiveReminders() -> [ReminderEntity] {
        return ReminderEntity.fetchActiveReminders(in: context)
    }

    /// 同步所有激活的提醒到通知系统
    func syncRemindersToNotifications(notificationManager: NotificationManager) async {
        let activeReminders = getActiveReminders()

        // 取消所有现有通知
        notificationManager.cancelAllNotifications()

        // 为所有激活的提醒重新调度通知
        for reminder in activeReminders {
            await notificationManager.scheduleNotification(for: reminder)
        }
    }

    /// 更新提醒信息
    func updateReminder(_ reminder: ReminderEntity, time: Date, message: String, isActive: Bool) async {
        await withCheckedContinuation { continuation in
            context.perform {
                reminder.time = time
                reminder.message = message
                reminder.isActive = isActive
                CoreDataManager.save(context: self.context)
                continuation.resume()
            }
        }
        await refreshData()
    }
    
    // MARK: - User Info Operations
    func updateUserGender(_ gender: UserInfoEntity.Gender) async {
        await withCheckedContinuation { continuation in
            context.perform {
                if let userInfo = self.userInfo {
                    userInfo.gender = gender.rawValue
                    CoreDataManager.save(context: self.context)
                }
                continuation.resume()
            }
        }
    }
    
    func updateUserBirthDate(_ birthDate: Date) async {
        await withCheckedContinuation { continuation in
            context.perform {
                if let userInfo = self.userInfo {
                    userInfo.birthDate = birthDate
                    CoreDataManager.save(context: self.context)
                }
                continuation.resume()
            }
        }
    }
    
    func consumeApiCall() async -> Bool {
        return await withCheckedContinuation { continuation in
            context.perform {
                let success = self.userInfo?.consumeCalls() ?? false
                if success {
                    CoreDataManager.save(context: self.context)
                }
                continuation.resume(returning: success)
            }
        }
    }
    
    func addApiCalls(_ count: Int32) async {
        await withCheckedContinuation { continuation in
            context.perform {
                self.userInfo?.addCalls(count)
                CoreDataManager.save(context: self.context)
                continuation.resume()
            }
        }
    }
    
    // MARK: - Data Clearing Operations

    /// 清除所有数据（包括本地和iCloud数据）
    /// - Returns: 清除操作是否成功
    func clearAllData() async throws {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                do {
                    // 1. 删除所有记录
                    let recordRequest: NSFetchRequest<RecordEntity> = RecordEntity.fetchRequest()
                    let allRecords = try self.context.fetch(recordRequest)
                    allRecords.forEach { self.context.delete($0) }

                    // 2. 删除所有报告
                    let reportRequest: NSFetchRequest<ReportEntity> = ReportEntity.fetchRequest()
                    let allReports = try self.context.fetch(reportRequest)
                    allReports.forEach { self.context.delete($0) }

                    // 3. 删除所有提醒
                    let reminderRequest: NSFetchRequest<ReminderEntity> = ReminderEntity.fetchRequest()
                    let allReminders = try self.context.fetch(reminderRequest)
                    allReminders.forEach { self.context.delete($0) }

                    // 4. 重置用户信息（保持实体但重置数据，不重新获得免费调用次数）
                    if let userInfo = self.userInfo {
                        userInfo.gender = UserInfoEntity.Gender.notSet.rawValue
                        userInfo.birthDate = Date()
                        // 注意：不重置 remainingCalls，确保用户不会重新获得免费调用次数
                    }

                    // 5. 保存更改
                    try self.context.save()

                    continuation.resume()
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }

    // MARK: - Utility Methods
    func clearErrorMessage() {
        errorMessage = nil
    }

    /// 获取统计信息
    func getStatistics() -> (totalRecords: Int, totalReports: Int, activeReminders: Int) {
        return (
            totalRecords: records.count,
            totalReports: reports.count,
            activeReminders: reminders.filter { $0.isActive }.count
        )
    }

    /// 计算坚持记录天数（有记录的不重复日期数量）
    func getRecordDaysCount() -> Int {
        let calendar = Calendar.current
        let uniqueDates = Set(records.compactMap { record -> Date? in
            guard let timestamp = record.timestamp else { return nil }
            return calendar.startOfDay(for: timestamp)
        })
        return uniqueDates.count
    }
}
