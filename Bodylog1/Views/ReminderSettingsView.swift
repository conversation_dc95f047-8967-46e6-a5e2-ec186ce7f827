//
//  ReminderSettingsView.swift
//  Bodylog1
//
//  Created by rainkygong on 2025/7/21.
//

import SwiftUI

/// 提醒设置主界面
struct ReminderSettingsView: View {
    @ObservedObject var dataViewModel: DataViewModel
    @EnvironmentObject var notificationManager: NotificationManager
    
    @State private var showingAddReminder = false
    @State private var editingReminder: ReminderEntity?
    @State private var showingPermissionAlert = false
    @State private var showingDeleteAlert = false
    @State private var reminderToDelete: ReminderEntity?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 权限状态提示
                if !notificationManager.isNotificationEnabled {
                    permissionBanner
                }
                
                // 提醒列表
                if dataViewModel.reminders.isEmpty {
                    emptyStateView
                } else {
                    reminderListView
                }
                
                Spacer()
            }
            .navigationTitle("提醒设置")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("添加") {
                        if notificationManager.isNotificationEnabled {
                            showingAddReminder = true
                        } else {
                            showingPermissionAlert = true
                        }
                    }
                    .font(.custom("PingFang SC", size: 16))
                    .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                }
            }
        }
        .sheet(isPresented: $showingAddReminder) {
            ReminderEditView(
                dataViewModel: dataViewModel,
                reminder: nil
            )
            .environmentObject(notificationManager)
        }
        .sheet(item: $editingReminder) { reminder in
            ReminderEditView(
                dataViewModel: dataViewModel,
                reminder: reminder
            )
            .environmentObject(notificationManager)
        }
        .alert("需要通知权限", isPresented: $showingPermissionAlert) {
            Button("去设置") {
                notificationManager.openSettings()
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text("请在设置中开启通知权限，以便接收提醒通知")
        }
        .alert("删除提醒", isPresented: $showingDeleteAlert) {
            Button("删除", role: .destructive) {
                if let reminder = reminderToDelete {
                    deleteReminder(reminder)
                }
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text("确定要删除这个提醒吗？")
        }
        .onAppear {
            notificationManager.checkAuthorizationStatus()
        }
    }
    
    // MARK: - 权限横幅
    private var permissionBanner: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "bell.slash")
                    .foregroundColor(.orange)
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("通知权限未开启")
                        .font(.custom("PingFang SC", size: 16))
                        .fontWeight(.medium)
                    
                    Text("开启通知权限以接收提醒")
                        .font(.custom("PingFang SC", size: 14))
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Button("开启") {
                    Task {
                        await notificationManager.requestAuthorization()
                    }
                }
                .font(.custom("PingFang SC", size: 14))
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(Color.orange)
                .cornerRadius(8)
            }
        }
        .padding()
        .background(Color.orange.opacity(0.1))
        .cornerRadius(12)
        .padding(.horizontal, 20)
        .padding(.top, 10)
    }
    
    // MARK: - 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Spacer()
            
            Image(systemName: "bell")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("还没有设置提醒")
                .font(.custom("PingFang SC", size: 18))
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            Text("添加提醒来帮助您养成记录习惯")
                .font(.custom("PingFang SC", size: 14))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("添加第一个提醒") {
                if notificationManager.isNotificationEnabled {
                    showingAddReminder = true
                } else {
                    showingPermissionAlert = true
                }
            }
            .font(.custom("PingFang SC", size: 16))
            .foregroundColor(.white)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(Color(red: 125/255, green: 175/255, blue: 106/255))
            .cornerRadius(10)
            
            Spacer()
        }
        .padding(.horizontal, 40)
    }
    
    // MARK: - 提醒列表视图
    private var reminderListView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(dataViewModel.reminders, id: \.id) { reminder in
                    ReminderRowView(
                        reminder: reminder,
                        notificationManager: notificationManager,
                        onToggle: { toggleReminder(reminder) },
                        onEdit: { editingReminder = reminder },
                        onDelete: { 
                            reminderToDelete = reminder
                            showingDeleteAlert = true
                        }
                    )
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
        }
    }
    
    // MARK: - 操作方法
    private func toggleReminder(_ reminder: ReminderEntity) {
        Task {
            await dataViewModel.toggleReminderActive(reminder)
            await notificationManager.updateNotification(for: reminder)
        }
    }
    
    private func deleteReminder(_ reminder: ReminderEntity) {
        notificationManager.cancelNotification(for: reminder)
        Task {
            await dataViewModel.deleteReminder(reminder)
        }
    }
}

// MARK: - 提醒行视图
struct ReminderRowView: View {
    let reminder: ReminderEntity
    let notificationManager: NotificationManager
    let onToggle: () -> Void
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    var body: some View {
        HStack(spacing: 16) {
            // 时间显示
            VStack(alignment: .leading, spacing: 4) {
                Text(reminder.formattedTime)
                    .font(.custom("PingFang SC", size: 18))
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text(reminder.message ?? "")
                    .font(.custom("PingFang SC", size: 14))
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
            
            // 开关
            Toggle("", isOn: Binding(
                get: { reminder.isActive },
                set: { _ in onToggle() }
            ))
            .toggleStyle(SwitchToggleStyle(tint: Color(red: 125/255, green: 175/255, blue: 106/255)))
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        .contextMenu {
            Button("编辑") {
                onEdit()
            }
            
            Button("删除", role: .destructive) {
                onDelete()
            }
        }
        .onTapGesture {
            onEdit()
        }
    }
}

// MARK: - 预览
struct ReminderSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        ReminderSettingsViewPreview()
    }
}
