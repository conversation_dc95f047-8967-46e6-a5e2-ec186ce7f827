//
//  DateRangePickerView.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/20.
//

import SwiftUI

/// 时间范围选择弹窗
struct DateRangePickerView: View {
    @Binding var isPresented: Bool
    @Binding var startDate: Date
    @Binding var endDate: Date
    let onConfirm: () -> Void
    
    @State private var tempStartDate: Date
    @State private var tempEndDate: Date
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    init(
        isPresented: Binding<Bool>,
        startDate: Binding<Date>,
        endDate: Binding<Date>,
        onConfirm: @escaping () -> Void
    ) {
        self._isPresented = isPresented
        self._startDate = startDate
        self._endDate = endDate
        self.onConfirm = onConfirm
        
        // 初始化临时日期
        self._tempStartDate = State(initialValue: startDate.wrappedValue)
        self._tempEndDate = State(initialValue: endDate.wrappedValue)
    }
    
    var body: some View {
        ZStack {
            // 背景遮罩
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isPresented = false
                    }
                }
            
            // 弹窗内容
            VStack(spacing: 0) {
                // 标题栏
                HStack {
                    Button("取消") {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isPresented = false
                        }
                    }
                    .foregroundColor(.gray)
                    
                    Spacer()
                    
                    Text("选择时间范围")
                        .font(.custom("PingFang SC", size: 18))
                        .fontWeight(.semibold)
                        .foregroundColor(Color(red: 34/255, green: 34/255, blue: 34/255))
                    
                    Spacer()
                    
                    Button("确定") {
                        confirmSelection()
                    }
                    .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                    .fontWeight(.medium)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
                .background(Color.white)
                
                Divider()
                
                // 日期选择器内容
                VStack(spacing: 24) {
                    // 开始日期
                    VStack(alignment: .leading, spacing: 8) {
                        Text("开始日期")
                            .font(.custom("PingFang SC", size: 16))
                            .fontWeight(.medium)
                            .foregroundColor(Color(red: 34/255, green: 34/255, blue: 34/255))
                        
                        DatePicker(
                            "",
                            selection: $tempStartDate,
                            in: ...Date(),
                            displayedComponents: .date
                        )
                        .datePickerStyle(.compact)
                        .labelsHidden()
                        .environment(\.locale, Locale(identifier: "zh_CN"))
                    }
                    
                    // 结束日期
                    VStack(alignment: .leading, spacing: 8) {
                        Text("结束日期")
                            .font(.custom("PingFang SC", size: 16))
                            .fontWeight(.medium)
                            .foregroundColor(Color(red: 34/255, green: 34/255, blue: 34/255))
                        
                        DatePicker(
                            "",
                            selection: $tempEndDate,
                            in: ...Date(),
                            displayedComponents: .date
                        )
                        .datePickerStyle(.compact)
                        .labelsHidden()
                        .environment(\.locale, Locale(identifier: "zh_CN"))
                    }
                    
                    // 快捷选择按钮
                    VStack(spacing: 12) {
                        Text("快捷选择")
                            .font(.custom("PingFang SC", size: 14))
                            .foregroundColor(.gray)
                        
                        HStack(spacing: 12) {
                            QuickSelectButton(title: "最近7天") {
                                setQuickRange(days: 7)
                            }
                            
                            QuickSelectButton(title: "最近30天") {
                                setQuickRange(days: 30)
                            }
                            
                            QuickSelectButton(title: "最近90天") {
                                setQuickRange(days: 90)
                            }
                        }
                    }
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 24)
                .background(Color.white)
            }
            .frame(maxWidth: 340)
            .background(Color.white)
            .cornerRadius(16)
            .shadow(color: .black.opacity(0.1), radius: 20, x: 0, y: 10)
        }
        .alert("提示", isPresented: $showingAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
        .environment(\.locale, Locale(identifier: "zh_CN"))
    }
    
    /// 快捷选择按钮
    private struct QuickSelectButton: View {
        let title: String
        let action: () -> Void
        
        var body: some View {
            Button(action: action) {
                Text(title)
                    .font(.custom("PingFang SC", size: 14))
                    .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color(red: 125/255, green: 175/255, blue: 106/255), lineWidth: 1)
                    )
            }
        }
    }
    
    /// 设置快捷时间范围
    private func setQuickRange(days: Int) {
        let calendar = Calendar.current
        tempEndDate = Date()
        tempStartDate = calendar.date(byAdding: .day, value: -days, to: tempEndDate) ?? tempEndDate
    }
    
    /// 确认选择
    private func confirmSelection() {
        // 验证日期范围
        if tempStartDate > tempEndDate {
            alertMessage = "开始日期不能晚于结束日期"
            showingAlert = true
            return
        }
        
        // 检查时间范围是否过长（超过1年）
        let calendar = Calendar.current
        if let daysDifference = calendar.dateComponents([.day], from: tempStartDate, to: tempEndDate).day,
           daysDifference > 365 {
            alertMessage = "时间范围不能超过1年"
            showingAlert = true
            return
        }
        
        // 更新绑定的日期
        startDate = tempStartDate
        endDate = tempEndDate
        
        // 关闭弹窗并执行确认回调
        withAnimation(.easeInOut(duration: 0.3)) {
            isPresented = false
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            onConfirm()
        }
    }
}
