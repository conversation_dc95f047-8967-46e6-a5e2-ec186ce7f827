//
//  BodyArchiveView.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/20.
//

import SwiftUI
import UniformTypeIdentifiers

/// 身体档案显示页面
struct BodyArchiveView: View {
    let archiveContent: String
    let timeRange: String
    let records: [RecordEntity] // 添加记录数据用于图表显示
    @Environment(\.dismiss) private var dismiss
    @State private var showingShareSheet = false
    @State private var showingPDFExport = false
    @State private var showingCharts = false // 控制图表显示
    @State private var isGeneratingPDF = false
    @State private var pdfShareItem: URL?
    @State private var showingPDFShareSheet = false
    @State private var showingPDFError = false
    @State private var pdfErrorMessage = ""
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景渐变
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 248/255, green: 252/255, blue: 249/255),
                        Color.white
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 0) {
                        // 档案头部
                        VStack(spacing: 16) {
                            // 标题
                            Text("身体档案")
                                .font(.custom("PingFang SC", size: 24))
                                .fontWeight(.bold)
                                .foregroundColor(Color(red: 34/255, green: 34/255, blue: 34/255))
                            
                            // 时间范围
                            Text(timeRange)
                                .font(.custom("PingFang SC", size: 16))
                                .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(
                                    Capsule()
                                        .fill(Color(red: 125/255, green: 175/255, blue: 106/255).opacity(0.1))
                                )
                            
                            // 生成时间
                            Text("生成时间: \(formattedCurrentTime)")
                                .font(.custom("PingFang SC", size: 14))
                                .foregroundColor(.gray)
                            
                            // 医疗用途提示
                            HStack {
                                Image(systemName: "stethoscope")
                                    .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                                Text("此档案可用于就医时向医生提供参考")
                                    .font(.custom("PingFang SC", size: 14))
                                    .foregroundColor(Color(red: 68/255, green: 68/255, blue: 68/255))
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color(red: 125/255, green: 175/255, blue: 106/255).opacity(0.05))
                            )
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                        .padding(.bottom, 24)
                        
                        // 档案内容
                        VStack(alignment: .leading, spacing: 0) {
                            MarkdownContentView(content: archiveContent)
                                .padding(.horizontal, 20)
                                .padding(.vertical, 24)
                        }
                        .background(Color.white)
                        .cornerRadius(16)
                        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
                        .padding(.horizontal, 16)

                        // 图表显示按钮
                        Button(action: {
                            showingCharts.toggle()
                        }) {
                            HStack {
                                Image(systemName: showingCharts ? "chart.bar.fill" : "chart.bar")
                                    .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                                Text(showingCharts ? "隐藏图表" : "显示图表")
                                    .font(.custom("PingFang SC", size: 16))
                                    .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                                Spacer()
                                Image(systemName: showingCharts ? "chevron.up" : "chevron.down")
                                    .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                            }
                            .padding(.horizontal, 20)
                            .padding(.vertical, 16)
                        }
                        .background(Color.white)
                        .cornerRadius(12)
                        .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
                        .padding(.horizontal, 16)
                        .padding(.top, 16)

                        // 图表内容
                        if showingCharts {
                            BodyArchiveChartsView(records: records)
                                .padding(.top, 16)
                                .transition(.opacity.combined(with: .scale))
                        }

                        Spacer(minLength: 100)
                    }
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                    .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack(spacing: 16) {
                        // PDF导出按钮
                        Button(action: {
                            showingPDFExport = true
                        }) {
                            Image(systemName: "doc.text")
                                .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                        }
                        
                        // 分享按钮
                        Button(action: {
                            showingShareSheet = true
                        }) {
                            Image(systemName: "square.and.arrow.up")
                                .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                        }
                    }
                }
            }
        }
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(items: [generateShareText()])
        }
        .alert("PDF导出", isPresented: $showingPDFExport) {
            Button("取消", role: .cancel) { }
            Button("导出") {
                Task {
                    await exportToPDF()
                }
            }
        } message: {
            Text("将身体档案导出为PDF文件，可直接打印给医生")
        }
        // PDF生成错误提示
        .alert("PDF导出失败", isPresented: $showingPDFError) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(pdfErrorMessage)
        }
        // PDF分享
        .sheet(isPresented: $showingPDFShareSheet) {
            if let pdfURL = pdfShareItem {
                ActivityViewController(activityItems: [pdfURL])
            }
        }
        // PDF生成加载指示器
        .overlay {
            if isGeneratingPDF {
                ZStack {
                    Color.black.opacity(0.3)
                        .ignoresSafeArea()

                    VStack(spacing: 16) {
                        ProgressView()
                            .scaleEffect(1.2)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))

                        Text("正在生成PDF...")
                            .font(.custom("PingFang SC", size: 16))
                            .foregroundColor(.white)
                    }
                    .padding(24)
                    .background(Color.black.opacity(0.8))
                    .cornerRadius(12)
                }
            }
        }
    }
    
    /// 格式化当前时间
    private var formattedCurrentTime: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: Date())
    }
    
    /// 生成分享文本
    private func generateShareText() -> String {
        return """
        身体档案
        时间范围: \(timeRange)
        生成时间: \(formattedCurrentTime)
        
        \(archiveContent)
        
        ——来自身体日记App
        """
    }
    
    /// 导出PDF
    private func exportToPDF() async {
        isGeneratingPDF = true

        do {
            // 生成PDF数据
            guard let pdfData = PDFGeneratorService.shared.generateBodyArchivePDF(
                archiveContent: archiveContent,
                timeRange: timeRange,
                userInfo: nil // 可以传入用户信息
            ) else {
                throw PDFExportError.generationFailed
            }

            // 创建临时文件
            let tempURL = try await savePDFToTemporaryFile(data: pdfData)

            await MainActor.run {
                isGeneratingPDF = false
                pdfShareItem = tempURL
                showingPDFShareSheet = true
            }

        } catch {
            await MainActor.run {
                isGeneratingPDF = false
                pdfErrorMessage = "PDF生成失败：\(error.localizedDescription)"
                showingPDFError = true
            }
        }
    }

    /// 保存PDF到临时文件
    private func savePDFToTemporaryFile(data: Data) async throws -> URL {
        let tempDirectory = FileManager.default.temporaryDirectory
        let fileName = "身体档案_\(timeRange)_\(Date().timeIntervalSince1970).pdf"
        let tempURL = tempDirectory.appendingPathComponent(fileName)

        try data.write(to: tempURL)
        return tempURL
    }
}

/// PDF导出错误类型
enum PDFExportError: LocalizedError {
    case generationFailed
    case saveFailed

    var errorDescription: String? {
        switch self {
        case .generationFailed:
            return "PDF生成失败"
        case .saveFailed:
            return "PDF保存失败"
        }
    }
}

/// 系统分享视图控制器
struct ActivityViewController: UIViewControllerRepresentable {
    let activityItems: [Any]
    let applicationActivities: [UIActivity]? = nil

    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(
            activityItems: activityItems,
            applicationActivities: applicationActivities
        )
        return controller
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {
        // 不需要更新
    }
}

// 复用AnalysisReportView中的Markdown相关组件
// 这些组件已经在AnalysisReportView.swift中定义，这里不需要重复定义
