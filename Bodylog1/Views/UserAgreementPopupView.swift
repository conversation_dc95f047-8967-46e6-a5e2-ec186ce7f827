//
//  UserAgreementPopupView.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/21.
//

import SwiftUI

/// 用户协议和隐私政策弹窗视图
struct UserAgreementPopupView: View {
    
    // MARK: - Properties
    @Binding var isPresented: Bool
    @State private var showUserAgreement = false
    @State private var showPrivacyPolicy = false
    @State private var isAnimating = false
    
    // MARK: - Callbacks
    let onAgree: () -> Void
    let onDisagree: () -> Void
    
    var body: some View {
        ZStack {
            // 背景遮罩
            Color.black.opacity(0.6)
                .ignoresSafeArea()
                .onTapGesture {
                    // 点击背景不关闭弹窗，用户必须做出选择
                }
            
            // 弹窗内容
            VStack(spacing: 0) {
                popupContent
            }
            .background(Color.white)
            .cornerRadius(20)
            .shadow(color: Color.black.opacity(0.2), radius: 20, x: 0, y: 10)
            .padding(.horizontal, 24)
            .scaleEffect(isAnimating ? 1.0 : 0.8)
            .opacity(isAnimating ? 1.0 : 0.0)
            .onAppear {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    isAnimating = true
                }
            }
        }
        .sheet(isPresented: $showUserAgreement) {
            UserAgreementView()
        }
        .sheet(isPresented: $showPrivacyPolicy) {
            PrivacyPolicyView()
        }
    }
    
    // MARK: - Popup Content
    private var popupContent: some View {
        VStack(spacing: 0) {
            // 标题
            titleSection
            
            // 内容文本
            contentSection
            
            // 按钮区域
            buttonSection
        }
    }
    
    // MARK: - Title Section
    private var titleSection: some View {
        VStack(spacing: 16) {
            // 图标
            Image(systemName: "doc.text.fill")
                .font(.system(size: 32, weight: .medium))
                .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
            
            // 标题
            Text("用户协议和隐私政策")
                .font(.custom("PingFang SC", size: 20))
                .fontWeight(.bold)
                .foregroundColor(Color(red: 34/255, green: 34/255, blue: 34/255))
        }
        .padding(.top, 32)
        .padding(.bottom, 24)
    }
    
    // MARK: - Content Section
    private var contentSection: some View {
        VStack(spacing: 16) {
            // 主要文本内容
            VStack(spacing: 12) {
                Text("欢迎您使用「身体日记」！")
                    .font(.custom("PingFang SC", size: 16))
                    .fontWeight(.medium)
                    .foregroundColor(Color(red: 34/255, green: 34/255, blue: 34/255))
                
                // 包含链接的文本
                VStack(spacing: 8) {
                    Text("为向您提供更贴心的服务和更优质的体验，在您使用本应用之前，请您认真阅读")
                        .font(.custom("PingFang SC", size: 14))
                        .foregroundColor(Color(red: 85/255, green: 85/255, blue: 85/255))
                        .multilineTextAlignment(.center)
                    
                    // 链接行
                    HStack(spacing: 4) {
                        Button(action: {
                            showUserAgreement = true
                        }) {
                            Text("《用户协议》")
                                .font(.custom("PingFang SC", size: 14))
                                .fontWeight(.medium)
                                .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                                .underline()
                        }
                        
                        Text("和")
                            .font(.custom("PingFang SC", size: 14))
                            .foregroundColor(Color(red: 85/255, green: 85/255, blue: 85/255))
                        
                        Button(action: {
                            showPrivacyPolicy = true
                        }) {
                            Text("《隐私政策》")
                                .font(.custom("PingFang SC", size: 14))
                                .fontWeight(.medium)
                                .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                                .underline()
                        }
                    }
                    
                    Text("全部条款，您同意并接受后开始使用我们的服务。")
                        .font(.custom("PingFang SC", size: 14))
                        .foregroundColor(Color(red: 85/255, green: 85/255, blue: 85/255))
                        .multilineTextAlignment(.center)
                }
            }
            .padding(.horizontal, 24)
            .lineSpacing(4)
        }
        .padding(.bottom, 32)
    }
    
    // MARK: - Button Section
    private var buttonSection: some View {
        VStack(spacing: 0) {
            // 分割线
            Divider()
                .background(Color(red: 240/255, green: 240/255, blue: 240/255))
            
            HStack(spacing: 0) {
                // 不同意按钮
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isAnimating = false
                    }
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        onDisagree()
                    }
                }) {
                    Text("不同意")
                        .font(.custom("PingFang SC", size: 16))
                        .fontWeight(.medium)
                        .foregroundColor(Color(red: 153/255, green: 153/255, blue: 153/255))
                        .frame(maxWidth: .infinity)
                        .frame(height: 56)
                        .background(Color.clear)
                }
                
                // 垂直分割线
                Divider()
                    .background(Color(red: 240/255, green: 240/255, blue: 240/255))
                    .frame(height: 56)
                
                // 同意并继续按钮
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isAnimating = false
                    }
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        onAgree()
                        isPresented = false
                    }
                }) {
                    Text("同意并继续")
                        .font(.custom("PingFang SC", size: 16))
                        .fontWeight(.semibold)
                        .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                        .frame(maxWidth: .infinity)
                        .frame(height: 56)
                        .background(Color.clear)
                }
            }
        }
    }
}

// MARK: - Preview
#Preview {
    UserAgreementPopupView(
        isPresented: .constant(true),
        onAgree: {
            print("用户同意协议")
        },
        onDisagree: {
            print("用户不同意协议")
        }
    )
}
