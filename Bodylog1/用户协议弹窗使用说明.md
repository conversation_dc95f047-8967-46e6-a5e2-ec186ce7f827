# 用户协议和隐私政策弹窗功能使用说明

## 功能概述

用户协议和隐私政策弹窗功能已成功集成到"身体日记"应用中，确保用户在首次使用应用时必须阅读并同意相关协议。

## 自动触发

### 首次启动
- 用户首次安装并启动应用时，会在应用加载完成后自动显示弹窗
- 弹窗为模态显示，用户必须做出选择才能继续使用应用

### 协议更新
- 当协议版本更新时，已有用户也会重新看到弹窗
- 确保所有用户都同意最新版本的协议

## 用户操作

### 查看协议详情
1. 点击弹窗中的"《用户协议》"链接
2. 系统会以Sheet形式展示完整的用户协议内容
3. 用户可以滚动查看所有条款
4. 点击"返回"按钮回到弹窗

### 查看隐私政策
1. 点击弹窗中的"《隐私政策》"链接
2. 系统会以Sheet形式展示完整的隐私政策内容
3. 用户可以滚动查看所有条款
4. 点击"返回"按钮回到弹窗

### 同意协议
1. 点击"同意并继续"按钮
2. 系统记录用户同意状态和时间戳
3. 弹窗关闭，用户可以正常使用应用
4. 后续启动应用不会再显示弹窗

### 拒绝协议
1. 点击"不同意"按钮
2. 应用会在0.5秒后自动退出
3. 用户需要重新启动应用并同意协议才能使用

## 开发者测试

### 测试弹窗显示
如果需要测试弹窗功能，可以使用以下方法：

1. **重置协议状态**：
   ```swift
   UserAgreementManager.shared.resetAgreementStatus()
   ```

2. **检查当前状态**：
   ```swift
   let needsToShow = UserAgreementManager.shared.shouldShowAgreementPopup()
   print("需要显示弹窗: \(needsToShow)")
   ```

3. **使用调试视图**：
   - 可以使用 `UserAgreementDebugView` 进行测试
   - 该视图提供了重置状态、显示弹窗等测试功能

### 调试信息
在Debug模式下，可以打印详细的调试信息：
```swift
#if DEBUG
UserAgreementManager.shared.printDebugInfo()
#endif
```

## 技术细节

### 状态存储
- 使用UserDefaults进行本地存储
- 存储用户同意状态、同意日期和协议版本
- 支持跨应用启动的状态持久化

### 协议版本管理
- 当前版本：1.0.0
- 版本更新时会自动触发重新显示弹窗
- 确保用户始终同意最新版本的协议

### 兼容性
- 支持iOS 16.6及以上系统
- 适配不同屏幕尺寸的设备
- 支持横竖屏切换

## 注意事项

### 用户体验
- 弹窗采用友好的设计风格，与应用整体UI保持一致
- 提供清晰的操作指引和选择按钮
- 支持平滑的动画效果

### 法律合规
- 确保用户在使用应用前明确同意相关协议
- 记录用户同意的时间和协议版本
- 支持协议更新时的重新确认

### 数据安全
- 协议同意状态仅存储在用户设备本地
- 不会上传到服务器或第三方服务
- 用户可以通过重置应用数据来清除状态

## 故障排除

### 弹窗不显示
1. 检查用户是否已经同意过协议
2. 确认协议版本是否为最新
3. 检查UserDefaults中的存储状态

### 弹窗重复显示
1. 检查协议版本设置是否正确
2. 确认UserDefaults写入是否成功
3. 检查应用是否正确调用同意方法

### 链接无法打开
1. 确认UserAgreementView和PrivacyPolicyView存在
2. 检查Sheet展示逻辑是否正确
3. 确认导航栈状态正常

## 更新维护

### 协议内容更新
1. 修改UserAgreementView和PrivacyPolicyView中的内容
2. 更新UserAgreementManager中的协议版本号
3. 测试确认弹窗重新显示

### 功能扩展
- 可以添加更多的协议类型
- 支持多语言版本的协议
- 添加协议同意历史记录

---

**最后更新：** 2025年7月21日  
**版本：** 1.0.0
