//
//  ContentView_Previews.swift
//  Bodylog1
//
//  Created by rainkygong on 2025/7/20.
//

import SwiftUI

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentViewPreview()
    }
}

// 测试键盘管理器的预览
struct KeyboardManagerTest: View {
    @StateObject private var keyboardManager = KeyboardManager()
    @State private var text = ""
    
    var body: some View {
        VStack {
            Text("键盘管理器测试")
                .font(.title)
                .padding()
            
            TextEditor(text: $text)
                .frame(height: 200)
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(10)
                .padding()
            
            Text("键盘状态: \(keyboardManager.isKeyboardVisible ? "显示" : "隐藏")")
            Text("键盘高度: \(keyboardManager.keyboardHeight)")
            
            Button("隐藏键盘") {
                keyboardManager.dismissKeyboard()
            }
            .padding()
            
            Spacer()
        }
        .keyboardAdaptive(keyboardManager: keyboardManager)
    }
}

struct KeyboardManagerTest_Previews: PreviewProvider {
    static var previews: some View {
        KeyboardManagerTest()
    }
}
