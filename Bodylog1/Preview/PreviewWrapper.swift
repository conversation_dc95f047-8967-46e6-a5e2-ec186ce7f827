//
//  PreviewWrapper.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/21.
//

import SwiftUI
import CoreData

/// 预览环境包装器，提供安全的预览环境
struct PreviewWrapper<Content: View>: View {
    let content: () -> Content

    @StateObject private var notificationManager = NotificationManager()
    @StateObject private var cloudKitManager = CloudKitManager.shared

    init(@ViewBuilder content: @escaping () -> Content) {
        self.content = content
    }

    var body: some View {
        content()
            .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
            .environmentObject(notificationManager)
            .environmentObject(cloudKitManager)
    }
}

/// 预览专用的ContentView包装器
struct ContentViewPreview: View {
    var body: some View {
        PreviewWrapper {
            ContentView()
        }
    }
}

/// 预览专用的ReminderSettingsView包装器
struct ReminderSettingsViewPreview: View {
    var body: some View {
        PreviewWrapper {
            ReminderSettingsView(dataViewModel: DataViewModel.preview())
        }
    }
}

/// 预览专用的ReminderEditView包装器
struct ReminderEditViewPreview: View {
    var body: some View {
        PreviewWrapper {
            ReminderEditView(
                dataViewModel: DataViewModel.preview(),
                reminder: nil
            )
        }
    }
}
