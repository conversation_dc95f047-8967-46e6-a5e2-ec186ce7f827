//
//  Bodylog1App.swift
//  Bodylog1
//
//  Created by rainkygong on 2025/7/19.
//

import SwiftUI
import RevenueCat

@main
struct Bodylog1App: App {
    let persistenceController = PersistenceController.shared
    @StateObject private var notificationManager = NotificationManager()
    @StateObject private var cloudKitManager = CloudKitManager.shared

    // 初始化RevenueCat
    init() {
        configureRevenueCat()
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.managedObjectContext, persistenceController.container.viewContext)
                .environmentObject(notificationManager)
                .environmentObject(cloudKitManager)
                .ignoresSafeArea(.keyboard, edges: .bottom) // 忽略键盘安全区域
                .onAppear {
                    // 应用启动时检查通知权限
                    notificationManager.checkAuthorizationStatus()
                    // 检查iCloud状态
                    cloudKitManager.checkAccountStatus()
                }
        }
    }

    // MARK: - RevenueCat Configuration
    private func configureRevenueCat() {
        // 检查API Key是否已配置
        guard RevenueCatConfig.isAPIKeyConfigured else {
            print("⚠️ [Bodylog1App] RevenueCat API Key未配置，请在RevenueCatConfig.swift中设置正确的API Key")
            return
        }

        // 设置日志级别
        if RevenueCatConfig.Environment.current.isDevelopment {
            Purchases.logLevel = .debug
        } else {
            Purchases.logLevel = .info
        }

        // 配置RevenueCat
        Purchases.configure(withAPIKey: RevenueCatConfig.apiKey)

        print("✅ [Bodylog1App] RevenueCat配置完成")
    }
}
