# 分析报告功能使用说明

## 功能概述

身体日记App现已集成AI分析报告功能，用户可以选择时间范围，让AI根据记录生成专业的健康分析报告。

## 使用流程

### 1. 进入历史记录页面
- 在App底部导航栏点击"历史记录"
- 进入历史记录页面

### 2. 点击生成分析报告按钮
- 在历史记录页面底部，点击"生成分析报告"按钮
- 按钮位于页面左下角，为绿色边框样式

### 3. 选择时间范围
- 弹出时间范围选择器
- 可以手动选择开始和结束日期
- 也可以使用快捷选择：最近7天、最近30天、最近90天
- 时间范围限制：最长不超过1年

### 4. 确认生成
- 点击"确定"按钮开始生成报告
- 系统会显示加载指示器："正在生成分析报告..."

### 5. 查看报告
- 生成完成后自动打开报告页面
- 报告包含以下内容：
  - 概览总结
  - 症状趋势分析
  - 可能的风险和原因
  - 饮食与生活方式分析
  - 健康建议

### 6. 分享报告
- 在报告页面右上角点击分享按钮
- 可以通过微信、邮件等方式分享报告

## 技术特性

### AI模型
- 使用DeepSeek R1模型
- 基于循证医学知识
- 提供个性化健康建议

### 数据安全
- API密钥安全存储在Secrets.plist中
- 不会硬编码在代码中
- 支持iOS 16.6+系统

### 报告存储
- 生成的报告自动保存到CoreData
- 支持iCloud同步
- 可在个人中心查看历史报告

## 注意事项

### API调用次数
- 每次生成报告消耗1次API调用
- 新用户默认赠送10次调用
- 调用次数不足时需要购买

### 数据要求
- 选择的时间范围内必须有记录
- 建议至少有3-5条记录以获得更准确的分析
- 记录内容越详细，分析越准确

### 网络要求
- 需要稳定的网络连接
- 生成过程需要调用外部AI服务
- 建议在WiFi环境下使用

## 错误处理

### 常见错误及解决方案

1. **"用户信息不完整"**
   - 请先在个人中心完善性别和年龄信息

2. **"API调用次数不足"**
   - 请在个人中心购买更多调用次数

3. **"选择的时间范围内没有找到记录"**
   - 请选择有记录的时间范围
   - 或先添加一些身体记录

4. **"网络错误"**
   - 检查网络连接
   - 稍后重试

5. **"API密钥无效"**
   - 联系开发者检查配置

6. **"无法加载分析报告prompt模板"** ✅ 已修复
   - 问题：prompt模板文件未正确添加到项目Bundle
   - 解决：将"分析报告prompt.md"移动到Bodylog1目录并重新构建
   - 状态：已验证文件正确包含在Bundle中

## 开发者信息

### 文件结构
```
Bodylog1/
├── Services/
│   ├── ConfigManager.swift          # 配置管理
│   ├── DeepSeekService.swift        # AI服务调用
│   └── AnalysisReportService.swift  # 报告生成服务
├── Views/
│   ├── DateRangePickerView.swift    # 时间选择器
│   └── AnalysisReportView.swift     # 报告显示页面
├── Secrets.plist                    # API密钥配置
└── 分析报告prompt.md                # AI提示模板
```

### 主要类和方法

1. **AnalysisReportService**
   - `generateAnalysisReport()` - 生成分析报告主方法

2. **DeepSeekService**
   - `generateAnalysisReport(prompt:)` - 调用AI API

3. **ConfigManager**
   - `deepSeekAPIKey` - 获取API密钥
   - `isAPIKeyValid` - 验证密钥有效性

4. **DateRangePickerView**
   - 自定义时间范围选择器
   - 支持快捷选择和手动选择

5. **AnalysisReportView**
   - Markdown格式报告显示
   - 支持分享功能

## 更新日志

### v1.0.0 (2025-07-20)
- ✅ 集成DeepSeek R1 AI模型
- ✅ 实现时间范围选择功能
- ✅ 添加分析报告生成和显示
- ✅ 支持报告分享功能
- ✅ 完善错误处理机制
- ✅ 兼容iOS 16.6+系统
