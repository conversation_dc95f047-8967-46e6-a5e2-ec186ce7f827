//
//  NetworkMonitor.swift
//  Bodylog1
//
//  Created by rainkygong on 2025/7/21.
//

import Foundation
import Network
import Combine

/// 网络状态监控器
@MainActor
class NetworkMonitor: ObservableObject {
    static let shared = NetworkMonitor()

    // MARK: - Published Properties
    @Published var isConnected: Bool = true
    @Published var connectionType: ConnectionType = .unknown
    @Published var isExpensive: Bool = false

    // MARK: - Private Properties
    private let monitor = NWPathMonitor()
    private let queue = DispatchQueue(label: "NetworkMonitor")

    // MARK: - Connection Type Enum
    enum ConnectionType {
        case wifi
        case cellular
        case ethernet
        case unknown

        var description: String {
            switch self {
            case .wifi:
                return "Wi-Fi"
            case .cellular:
                return "蜂窝网络"
            case .ethernet:
                return "以太网"
            case .unknown:
                return "未知"
            }
        }

        var icon: String {
            switch self {
            case .wifi:
                return "wifi"
            case .cellular:
                return "antenna.radiowaves.left.and.right"
            case .ethernet:
                return "cable.connector"
            case .unknown:
                return "questionmark.circle"
            }
        }
    }

    // MARK: - Initialization
    private init() {
        startMonitoring()
    }

    deinit {
        // 直接调用stopMonitoring，不使用Task
        stopMonitoring()
    }

    // MARK: - Public Methods

    /// 开始监控网络状态
    func startMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.updateNetworkStatus(path: path)
            }
        }
        monitor.start(queue: queue)
    }

    /// 停止监控网络状态
    nonisolated func stopMonitoring() {
        monitor.cancel()
    }

    /// 检查是否适合进行数据同步
    var isSuitableForSync: Bool {
        return isConnected && (!isExpensive || connectionType == .wifi)
    }

    /// 获取网络状态描述
    var statusDescription: String {
        if !isConnected {
            return "网络未连接"
        }

        var description = "已连接 - \(connectionType.description)"
        if isExpensive {
            description += " (计费网络)"
        }
        return description
    }
}

// MARK: - Private Methods
private extension NetworkMonitor {

    /// 更新网络状态
    func updateNetworkStatus(path: NWPath) {
        isConnected = path.status == .satisfied
        isExpensive = path.isExpensive

        // 确定连接类型
        if path.usesInterfaceType(.wifi) {
            connectionType = .wifi
        } else if path.usesInterfaceType(.cellular) {
            connectionType = .cellular
        } else if path.usesInterfaceType(.wiredEthernet) {
            connectionType = .ethernet
        } else {
            connectionType = .unknown
        }

        // 通知CloudKit管理器网络状态变化
        CloudKitManager.shared.checkAccountStatus()
    }
}