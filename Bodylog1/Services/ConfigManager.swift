//
//  ConfigManager.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/20.
//

import Foundation

/// 配置管理器，用于安全地读取API密钥和其他配置信息
class ConfigManager {
    
    /// 单例实例
    static let shared = ConfigManager()
    
    private init() {}
    
    /// 从Secrets.plist文件中读取配置
    private lazy var secrets: [String: Any] = {
        guard let path = Bundle.main.path(forResource: "Secrets", ofType: "plist"),
              let plist = NSDictionary(contentsOfFile: path) as? [String: Any] else {
            print("⚠️ 警告: 无法找到或读取Secrets.plist文件")
            return [:]
        }
        return plist
    }()
    
    /// DeepSeek API密钥
    var deepSeekAPIKey: String {
        return secrets["DeepSeekAPIKey"] as? String ?? ""
    }
    
    /// DeepSeek API基础URL
    var deepSeekBaseURL: String {
        return secrets["DeepSeekBaseURL"] as? String ?? "https://api.deepseek.com/v1"
    }
    
    /// 检查API密钥是否有效
    var isAPIKeyValid: Bool {
        return !deepSeekAPIKey.isEmpty && deepSeekAPIKey.hasPrefix("sk-")
    }
}
