//
//  BodyArchiveService.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/20.
//

import Foundation

/// 身体档案服务，负责生成身体档案
class BodyArchiveService {
    
    /// 单例实例
    static let shared = BodyArchiveService()
    
    private init() {}
    
    /// 生成身体档案
    /// - Parameters:
    ///   - dataViewModel: 数据视图模型
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    /// - Returns: 生成的身体档案内容
    @MainActor
    func generateBodyArchive(
        dataViewModel: DataViewModel,
        startDate: Date,
        endDate: Date
    ) async throws -> String {

        // 1. 获取用户信息
        guard let userInfo = dataViewModel.userInfo else {
            throw ArchiveError.missingUserInfo
        }

        // 2. 检查API调用次数
        guard userInfo.remainingCalls > 0 else {
            throw ArchiveError.insufficientAPICalls
        }

        // 3. 获取时间范围内的记录
        let records = dataViewModel.getRecords(from: startDate, to: endDate)

        guard !records.isEmpty else {
            throw ArchiveError.noRecordsFound
        }

        // 4. 构建身体档案prompt
        let prompt = buildArchivePrompt(
            userInfo: userInfo,
            records: records,
            startDate: startDate,
            endDate: endDate
        )
        
        // 5. 调用AI生成身体档案
        let archiveContent: String
        do {
            archiveContent = try await DeepSeekService.shared.generateBodyArchive(prompt: prompt)
        } catch {
            // 将DeepSeek API错误转换为用户友好的错误
            if let apiError = error as? DeepSeekService.APIError {
                switch apiError {
                case .networkError(let message):
                    throw ArchiveError.networkError(message)
                case .apiError(let message):
                    throw ArchiveError.apiError(message)
                default:
                    throw ArchiveError.archiveGenerationFailed
                }
            } else {
                throw ArchiveError.archiveGenerationFailed
            }
        }

        // 6. 消费API调用次数
        let success = await dataViewModel.consumeApiCall()
        if !success {
            print("⚠️ 警告: API调用次数消费失败")
        }

        // 7. 保存身体档案到数据库
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "zh_CN")
        let timeRangeString = "\(formatter.string(from: startDate)) ~ \(formatter.string(from: endDate))"
        
        await dataViewModel.addReport(
            title: "身体档案 - \(timeRangeString)",
            type: .archive,
            content: archiveContent,
            timeRange: timeRangeString
        )

        return archiveContent
    }
    
    /// 构建身体档案prompt
    private func buildArchivePrompt(
        userInfo: UserInfoEntity,
        records: [RecordEntity],
        startDate: Date,
        endDate: Date
    ) -> String {
        
        // 读取prompt模板
        guard let templatePath = Bundle.main.path(forResource: "身体档案prompt", ofType: "md"),
              let template = try? String(contentsOfFile: templatePath) else {
            // 如果无法读取模板文件，使用默认模板
            return buildDefaultPrompt(userInfo: userInfo, records: records, startDate: startDate, endDate: endDate)
        }
        
        // 格式化日期
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        dateFormatter.locale = Locale(identifier: "zh_CN")
        
        // 格式化记录
        let recordsText = formatRecordsForPrompt(records)
        
        // 替换模板中的占位符
        var prompt = template
        prompt = prompt.replacingOccurrences(of: "{{gender}}", with: userInfo.userGender?.displayName ?? "未设置")
        prompt = prompt.replacingOccurrences(of: "{{age}}", with: "\(userInfo.age)")
        prompt = prompt.replacingOccurrences(of: "{{start_date}}", with: dateFormatter.string(from: startDate))
        prompt = prompt.replacingOccurrences(of: "{{end_date}}", with: dateFormatter.string(from: endDate))
        prompt = prompt.replacingOccurrences(of: "{{records}}", with: recordsText)
        
        return prompt
    }
    
    /// 构建默认prompt（当模板文件不可用时）
    private func buildDefaultPrompt(
        userInfo: UserInfoEntity,
        records: [RecordEntity],
        startDate: Date,
        endDate: Date
    ) -> String {
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        dateFormatter.locale = Locale(identifier: "zh_CN")
        
        let recordsText = formatRecordsForPrompt(records)
        
        return """
        你是一名健康日志数据分析专家，任务是根据用户提供的身体记录和背景信息，生成一份完整的"身体档案"。该档案将被医生或用户用作健康记录参考，因此内容必须完整、科学、可信。

        ### 重要规则：
        1. 对用户的症状频率、饮食与不适的关联、作息习惯等进行统计和简单分析。
        2. 输出简短的趋势总结。
        3. 不做医学诊断，只给出数据洞察和规律总结。
        4. **严禁包含任何图表描述、技术说明或Swift Charts相关内容**
        5. **不要提及"图表"、"数据源"、"绘制"等技术词汇**
        6. **只提供纯文字的健康分析和建议**

        ### 用户信息：
        - 性别：\(userInfo.userGender?.displayName ?? "未设置")
        - 年龄：\(userInfo.age) 岁

        ### 时间范围：
        \(dateFormatter.string(from: startDate)) ~ \(dateFormatter.string(from: endDate))

        ### 用户记录：
        \(recordsText)

        ### 需要输出的内容结构：
        1. **概览总结**
        2. **展示所有记录**
        3. **症状趋势总结**
        4. **饮食与不适关系总结**
        5. **作息与身体反馈总结**

        ### 输出格式：
        - 使用 Markdown 格式，包含标题、表格、段落描述。
        - **表格格式严格要求**：
          * 表头：| 日期时间 | 记录内容 | 事件类型 |
          * 使用标准Markdown表格语法：| --- | --- | --- |
          * 日期时间格式：2025/7/20 15:30
          * 记录内容要简洁，超过20字的内容要适当缩略
          * 事件类型分类：症状、饮食、作息、情绪、其他
          * 表格行要整齐对齐，每行内容不要过长
        - 只输出用户可读的分析内容，不包含技术描述。
        """
    }
    
    /// 格式化记录用于prompt
    private func formatRecordsForPrompt(_ records: [RecordEntity]) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy/M/d HH:mm"
        dateFormatter.locale = Locale(identifier: "zh_CN")

        return records.compactMap { record in
            guard let text = record.text,
                  let timestamp = record.timestamp else { return nil }

            let formattedDate = dateFormatter.string(from: timestamp)

            // 分析记录类型
            let eventType = analyzeEventType(text: text)

            return "日期时间：\(formattedDate) | 记录内容：\(text) | 事件类型：\(eventType)"
        }.joined(separator: "\n")
    }

    /// 分析记录事件类型
    private func analyzeEventType(text: String) -> String {
        let lowercaseText = text.lowercased()

        // 症状相关关键词
        let symptomKeywords = ["疼痛", "不适", "头痛", "胃痛", "腹痛", "发烧", "咳嗽", "疲劳", "乏力", "恶心", "呕吐", "腹泻", "便秘", "失眠", "头晕", "心悸", "胸闷", "过敏", "皮疹", "瘙痒"]

        // 饮食相关关键词
        let foodKeywords = ["吃了", "喝了", "早餐", "午餐", "晚餐", "零食", "水果", "蔬菜", "肉类", "海鲜", "奶制品", "咖啡", "茶", "酒", "饮料"]

        // 作息相关关键词
        let sleepKeywords = ["睡觉", "起床", "熬夜", "失眠", "午睡", "休息", "运动", "锻炼", "散步", "跑步"]

        // 情绪相关关键词
        let moodKeywords = ["开心", "难过", "焦虑", "紧张", "压力", "放松", "心情", "情绪"]

        if symptomKeywords.contains(where: { lowercaseText.contains($0) }) {
            return "症状"
        } else if foodKeywords.contains(where: { lowercaseText.contains($0) }) {
            return "饮食"
        } else if sleepKeywords.contains(where: { lowercaseText.contains($0) }) {
            return "作息"
        } else if moodKeywords.contains(where: { lowercaseText.contains($0) }) {
            return "情绪"
        } else {
            return "其他"
        }
    }
}

/// 身体档案错误类型
enum ArchiveError: LocalizedError {
    case missingUserInfo
    case insufficientAPICalls
    case noRecordsFound
    case networkError(String)
    case apiError(String)
    case archiveGenerationFailed
    
    var errorDescription: String? {
        switch self {
        case .missingUserInfo:
            return "用户信息缺失"
        case .insufficientAPICalls:
            return "API调用次数不足，请购买更多次数"
        case .noRecordsFound:
            return "选择的时间范围内没有找到记录"
        case .networkError(let message):
            return "网络错误：\(message)"
        case .apiError(let message):
            return "API错误：\(message)"
        case .archiveGenerationFailed:
            return "身体档案生成失败，请稍后重试"
        }
    }
}
