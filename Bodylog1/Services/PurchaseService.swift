//
//  PurchaseService.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/22.
//

import Foundation
import RevenueCat
import SwiftUI

/// 购买状态枚举
enum PurchaseState: Equatable {
    case idle
    case loading
    case success(String)
    case failed(String)
}

/// 购买错误类型
enum PurchaseError: LocalizedError {
    case noProductsAvailable
    case purchaseCancelled
    case networkError
    case unknownError(String)

    var errorDescription: String? {
        switch self {
        case .noProductsAvailable:
            return "暂无可用产品"
        case .purchaseCancelled:
            return "购买已取消"
        case .networkError:
            return "网络连接错误，请检查网络后重试"
        case .unknownError(let message):
            return "购买失败：\(message)"
        }
    }
}

/// RevenueCat购买服务
@MainActor
class PurchaseService: ObservableObject {
    
    // MARK: - Published Properties
    @Published var purchaseState: PurchaseState = .idle
    @Published var availablePackages: [Package] = []
    @Published var customerInfo: CustomerInfo?
    @Published var isLoading = false
    
    // MARK: - Private Properties
    private let dataViewModel: DataViewModel
    
    // MARK: - Constants
    private struct Constants {
        static let offeringIdentifier = "ai_calls" // Offering标识符
    }
    
    // MARK: - Initialization
    init(dataViewModel: DataViewModel) {
        self.dataViewModel = dataViewModel
        configureRevenueCat()
    }
    
    // MARK: - Configuration
    private func configureRevenueCat() {
        // 注意：RevenueCat已在App启动时配置，这里只需要设置用户ID和加载数据

        // 设置用户ID（使用设备ID作为匿名用户标识）
        let deviceID = UIDevice.current.identifierForVendor?.uuidString ?? UUID().uuidString
        Purchases.shared.logIn(deviceID) { customerInfo, created, error in
            if let error = error {
                print("❌ [PurchaseService] 用户登录失败: \(error.localizedDescription)")
            } else {
                print("✅ [PurchaseService] 用户登录成功, 用户ID: \(deviceID)")
            }
        }

        // 获取初始客户信息
        Task {
            await refreshCustomerInfo()
            await loadOfferings()
        }
    }
    
    // MARK: - Public Methods
    
    /// 刷新客户信息
    func refreshCustomerInfo() async {
        do {
            let customerInfo = try await Purchases.shared.customerInfo()
            await MainActor.run {
                self.customerInfo = customerInfo
            }
            print("✅ [PurchaseService] 客户信息刷新成功")
        } catch {
            await MainActor.run {
                self.purchaseState = .failed("获取客户信息失败: \(error.localizedDescription)")
            }
            print("❌ [PurchaseService] 客户信息刷新失败: \(error.localizedDescription)")
        }
    }
    
    /// 加载可用的产品包
    func loadOfferings() async {
        await MainActor.run {
            isLoading = true
            purchaseState = .loading
        }

        do {
            let offerings = try await Purchases.shared.offerings()

            await MainActor.run {
                if let offering = offerings.offering(identifier: Constants.offeringIdentifier) {
                    self.availablePackages = offering.availablePackages
                    self.purchaseState = .idle
                    print("✅ [PurchaseService] 成功加载 \(offering.availablePackages.count) 个产品包")
                } else {
                    self.availablePackages = []
                    self.purchaseState = .failed("未找到可用的产品包")
                    print("⚠️ [PurchaseService] 未找到Offering: \(Constants.offeringIdentifier)")
                }
                self.isLoading = false
            }
        } catch {
            await MainActor.run {
                self.isLoading = false
                self.purchaseState = .failed("加载产品失败: \(error.localizedDescription)")
            }
            print("❌ [PurchaseService] 加载产品失败: \(error.localizedDescription)")
        }
    }
    
    /// 购买产品包
    func purchase(package: Package) async -> Bool {
        await MainActor.run {
            isLoading = true
            purchaseState = .loading
        }

        do {
            let result = try await Purchases.shared.purchase(package: package)
            let customerInfo = result.customerInfo

            await MainActor.run {
                self.customerInfo = customerInfo
                self.isLoading = false
            }

            // 处理购买成功后的逻辑
            await handlePurchaseSuccess(package: package, customerInfo: customerInfo)

            await MainActor.run {
                self.purchaseState = .success("购买成功！已为您增加\(package.callCount)次调用")
            }

            print("✅ [PurchaseService] 购买成功: \(package.storeProduct.localizedTitle)")
            return true

        } catch {
            let errorMessage = handlePurchaseError(error)
            await MainActor.run {
                self.isLoading = false
                self.purchaseState = .failed(errorMessage)
            }
            print("❌ [PurchaseService] 购买失败: \(error.localizedDescription)")
            return false
        }
    }
    
    /// 恢复购买
    func restorePurchases() async -> Bool {
        await MainActor.run {
            isLoading = true
            purchaseState = .loading
        }

        do {
            let customerInfo = try await Purchases.shared.restorePurchases()

            await MainActor.run {
                self.customerInfo = customerInfo
                self.isLoading = false
                self.purchaseState = .success("购买恢复成功")
            }

            print("✅ [PurchaseService] 购买恢复成功")
            return true

        } catch {
            let errorMessage = handlePurchaseError(error)
            await MainActor.run {
                self.isLoading = false
                self.purchaseState = .failed(errorMessage)
            }
            print("❌ [PurchaseService] 恢复购买失败: \(error.localizedDescription)")
            return false
        }
    }
    
    // MARK: - Private Methods
    
    /// 处理购买成功后的逻辑
    private func handlePurchaseSuccess(package: Package, customerInfo: CustomerInfo) async {
        // 根据产品ID确定增加的调用次数
        let callsToAdd = getCallsForProduct(productId: package.storeProduct.productIdentifier)
        
        if callsToAdd > 0 {
            // 更新本地数据库中的调用次数
            await dataViewModel.addApiCalls(Int32(callsToAdd))
            print("✅ [PurchaseService] 已增加 \(callsToAdd) 次调用")
        }
    }
    
    /// 根据产品ID获取对应的调用次数
    private func getCallsForProduct(productId: String) -> Int {
        switch productId {
        case "bodylog_20_calls":
            return 20
        case "bodylog_50_calls":
            return 50
        default:
            print("⚠️ [PurchaseService] 未知的产品ID: \(productId)")
            return 0
        }
    }

    /// 处理购买错误
    private func handlePurchaseError(_ error: Error) -> String {
        // 检查是否是RevenueCat的错误类型
        let errorDescription = error.localizedDescription.lowercased()

        if errorDescription.contains("cancelled") || errorDescription.contains("取消") {
            return "购买已取消"
        } else if errorDescription.contains("network") || errorDescription.contains("网络") {
            return "网络连接错误，请检查网络后重试"
        } else if errorDescription.contains("not allowed") || errorDescription.contains("不允许") {
            return "当前设备不允许购买"
        } else if errorDescription.contains("invalid") || errorDescription.contains("无效") {
            return "购买信息无效"
        } else if errorDescription.contains("not available") || errorDescription.contains("不可用") {
            return "产品暂时不可购买"
        } else {
            return "购买失败：\(error.localizedDescription)"
        }
    }

    /// 重置购买状态
    func resetPurchaseState() {
        purchaseState = .idle
    }
}

// MARK: - Package Extensions
extension Package {
    /// 获取产品对应的调用次数
    var callCount: Int {
        switch storeProduct.productIdentifier {
        case "bodylog_20_calls":
            return 20
        case "bodylog_50_calls":
            return 50
        default:
            return 0
        }
    }

    /// 获取格式化的价格字符串
    var formattedPrice: String {
        return storeProduct.localizedPriceString
    }
}
