//
//  PDFGeneratorService.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/21.
//

import Foundation
import PDFKit
import UIKit

/// PDF生成服务
class PDFGeneratorService {
    
    /// 单例实例
    static let shared = PDFGeneratorService()
    
    private init() {}
    
    /// 生成身体档案PDF
    /// - Parameters:
    ///   - archiveContent: 身体档案内容
    ///   - timeRange: 时间范围
    ///   - userInfo: 用户信息（可选）
    /// - Returns: PDF数据
    func generateBodyArchivePDF(
        archiveContent: String,
        timeRange: String,
        userInfo: UserInfoEntity? = nil
    ) -> Data? {
        
        // 创建PDF页面格式
        let pageFormat = UIGraphicsPDFRendererFormat()
        pageFormat.documentInfo = [
            kCGPDFContextTitle as String: "身体档案",
            kCGPDFContextAuthor as String: "身体日记App",
            kCGPDFContextSubject as String: "个人健康档案",
            kCGPDFContextCreator as String: "身体日记App v1.0"
        ]
        
        // A4页面大小 (595.2 x 841.8 points)
        let pageRect = CGRect(x: 0, y: 0, width: 595.2, height: 841.8)
        let renderer = UIGraphicsPDFRenderer(bounds: pageRect, format: pageFormat)
        
        return renderer.pdfData { context in
            // 开始新页面
            context.beginPage()
            
            // 绘制PDF内容
            drawPDFContent(
                in: pageRect,
                archiveContent: archiveContent,
                timeRange: timeRange,
                userInfo: userInfo,
                context: context
            )
        }
    }
    
    /// 绘制PDF内容
    private func drawPDFContent(
        in pageRect: CGRect,
        archiveContent: String,
        timeRange: String,
        userInfo: UserInfoEntity?,
        context: UIGraphicsPDFRendererContext
    ) {
        let margin: CGFloat = 50
        let contentRect = CGRect(
            x: margin,
            y: margin,
            width: pageRect.width - 2 * margin,
            height: pageRect.height - 2 * margin
        )
        
        var currentY: CGFloat = contentRect.minY
        
        // 1. 绘制标题
        currentY = drawTitle(in: contentRect, startY: currentY)
        currentY += 20
        
        // 2. 绘制基本信息
        currentY = drawBasicInfo(
            in: contentRect,
            startY: currentY,
            timeRange: timeRange,
            userInfo: userInfo
        )
        currentY += 30
        
        // 3. 绘制医疗用途说明
        currentY = drawMedicalNotice(in: contentRect, startY: currentY)
        currentY += 20
        
        // 4. 绘制档案内容
        currentY = drawArchiveContent(
            in: contentRect,
            startY: currentY,
            content: archiveContent,
            context: context
        )
        
        // 5. 绘制页脚
        drawFooter(in: pageRect)
    }
    
    /// 绘制标题
    private func drawTitle(in rect: CGRect, startY: CGFloat) -> CGFloat {
        let title = "身体档案"
        let titleFont = UIFont.systemFont(ofSize: 24, weight: .bold)
        let titleAttributes: [NSAttributedString.Key: Any] = [
            .font: titleFont,
            .foregroundColor: UIColor.black
        ]
        
        let titleSize = title.size(withAttributes: titleAttributes)
        let titleRect = CGRect(
            x: rect.midX - titleSize.width / 2,
            y: startY,
            width: titleSize.width,
            height: titleSize.height
        )
        
        title.draw(in: titleRect, withAttributes: titleAttributes)
        
        // 绘制标题下划线
        let lineY = titleRect.maxY + 5
        let linePath = UIBezierPath()
        linePath.move(to: CGPoint(x: rect.minX, y: lineY))
        linePath.addLine(to: CGPoint(x: rect.maxX, y: lineY))
        linePath.lineWidth = 1.0
        UIColor.gray.setStroke()
        linePath.stroke()
        
        return lineY + 10
    }
    
    /// 绘制基本信息
    private func drawBasicInfo(
        in rect: CGRect,
        startY: CGFloat,
        timeRange: String,
        userInfo: UserInfoEntity?
    ) -> CGFloat {
        let font = UIFont.systemFont(ofSize: 12)
        let attributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: UIColor.darkGray
        ]
        
        var currentY = startY
        let lineHeight: CGFloat = 18
        
        // 生成时间
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        let currentTime = formatter.string(from: Date())
        
        let infoLines = [
            "生成时间：\(currentTime)",
            "时间范围：\(timeRange)",
            userInfo != nil ? "性别：\(userInfo?.gender ?? "未设置")" : nil,
            userInfo != nil ? "年龄：\(userInfo?.age ?? 0)岁" : nil
        ].compactMap { $0 }
        
        for line in infoLines {
            let lineRect = CGRect(x: rect.minX, y: currentY, width: rect.width, height: lineHeight)
            line.draw(in: lineRect, withAttributes: attributes)
            currentY += lineHeight
        }
        
        return currentY
    }
    
    /// 绘制医疗用途说明
    private func drawMedicalNotice(in rect: CGRect, startY: CGFloat) -> CGFloat {
        let notice = "📋 此档案可用于就医时向医生提供参考"
        let font = UIFont.systemFont(ofSize: 14, weight: .medium)
        let attributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: UIColor.systemBlue,
            .backgroundColor: UIColor.systemBlue.withAlphaComponent(0.1)
        ]
        
        let noticeSize = notice.size(withAttributes: attributes)
        let noticeRect = CGRect(
            x: rect.minX,
            y: startY,
            width: rect.width,
            height: noticeSize.height + 10
        )
        
        // 绘制背景
        UIColor.systemBlue.withAlphaComponent(0.1).setFill()
        UIBezierPath(roundedRect: noticeRect, cornerRadius: 5).fill()
        
        // 绘制文字
        let textRect = CGRect(
            x: noticeRect.minX + 10,
            y: noticeRect.minY + 5,
            width: noticeRect.width - 20,
            height: noticeRect.height - 10
        )
        notice.draw(in: textRect, withAttributes: attributes)
        
        return noticeRect.maxY
    }
    
    /// 绘制档案内容
    private func drawArchiveContent(
        in rect: CGRect,
        startY: CGFloat,
        content: String,
        context: UIGraphicsPDFRendererContext
    ) -> CGFloat {
        let font = UIFont.systemFont(ofSize: 11)
        let _ = 16 // lineHeight for future use
        let paragraphSpacing: CGFloat = 8
        
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = 2
        paragraphStyle.paragraphSpacing = paragraphSpacing
        
        let attributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: UIColor.black,
            .paragraphStyle: paragraphStyle
        ]
        
        // 处理Markdown格式的内容
        let processedContent = processMarkdownContent(content)
        
        let attributedString = NSAttributedString(string: processedContent, attributes: attributes)
        
        // 计算内容区域
        let contentRect = CGRect(
            x: rect.minX,
            y: startY,
            width: rect.width,
            height: rect.maxY - startY - 50 // 留出页脚空间
        )
        
        // 绘制内容，支持分页
        drawAttributedStringWithPagination(
            attributedString: attributedString,
            in: contentRect,
            context: context,
            pageRect: CGRect(x: 0, y: 0, width: 595.2, height: 841.8),
            margin: 50
        )
        
        return contentRect.maxY
    }
    
    /// 处理Markdown内容
    private func processMarkdownContent(_ content: String) -> String {
        var processed = content
        
        // 移除Markdown标记，保留纯文本
        processed = processed.replacingOccurrences(of: "**", with: "")
        processed = processed.replacingOccurrences(of: "*", with: "")
        processed = processed.replacingOccurrences(of: "###", with: "")
        processed = processed.replacingOccurrences(of: "##", with: "")
        processed = processed.replacingOccurrences(of: "#", with: "")
        
        // 处理表格标记
        processed = processed.replacingOccurrences(of: "|", with: " | ")
        
        return processed
    }
    
    /// 绘制带分页的属性字符串
    private func drawAttributedStringWithPagination(
        attributedString: NSAttributedString,
        in rect: CGRect,
        context: UIGraphicsPDFRendererContext,
        pageRect: CGRect,
        margin: CGFloat
    ) {
        // 使用简单的文本绘制方式，避免坐标变换问题
        let font = UIFont.systemFont(ofSize: 11)
        let paragraphSpacing: CGFloat = 8

        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = 2
        paragraphStyle.paragraphSpacing = paragraphSpacing

        let attributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: UIColor.black,
            .paragraphStyle: paragraphStyle
        ]

        let text = attributedString.string
        var currentY = rect.minY
        let maxY = rect.maxY

        // 按段落分割文本
        let paragraphs = text.components(separatedBy: .newlines)

        for paragraph in paragraphs {
            if paragraph.trimmingCharacters(in: .whitespaces).isEmpty {
                currentY += paragraphSpacing
                continue
            }

            // 计算段落高度
            let paragraphSize = paragraph.boundingRect(
                with: CGSize(width: rect.width, height: CGFloat.greatestFiniteMagnitude),
                options: [.usesLineFragmentOrigin, .usesFontLeading],
                attributes: attributes,
                context: nil
            )

            // 检查是否需要分页
            if currentY + paragraphSize.height > maxY && currentY > rect.minY {
                context.beginPage()
                currentY = margin

                // 在新页面绘制页眉
                drawPageHeader(in: pageRect, margin: margin)
                currentY += 30
            }

            // 绘制段落
            let drawRect = CGRect(x: rect.minX, y: currentY, width: rect.width, height: paragraphSize.height)
            paragraph.draw(in: drawRect, withAttributes: attributes)

            currentY += paragraphSize.height + paragraphSpacing
        }
    }
    
    /// 绘制页眉
    private func drawPageHeader(in pageRect: CGRect, margin: CGFloat) {
        let headerText = "身体档案"
        let font = UIFont.systemFont(ofSize: 12, weight: .medium)
        let attributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: UIColor.gray
        ]
        
        let headerRect = CGRect(
            x: margin,
            y: margin,
            width: pageRect.width - 2 * margin,
            height: 20
        )
        
        headerText.draw(in: headerRect, withAttributes: attributes)
        
        // 绘制分隔线
        let lineY = headerRect.maxY + 5
        let linePath = UIBezierPath()
        linePath.move(to: CGPoint(x: margin, y: lineY))
        linePath.addLine(to: CGPoint(x: pageRect.width - margin, y: lineY))
        linePath.lineWidth = 0.5
        UIColor.lightGray.setStroke()
        linePath.stroke()
    }
    
    /// 绘制页脚
    private func drawFooter(in pageRect: CGRect) {
        let footerText = "——来自身体日记App"
        let font = UIFont.systemFont(ofSize: 10)
        let attributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: UIColor.gray
        ]
        
        let footerSize = footerText.size(withAttributes: attributes)
        let footerRect = CGRect(
            x: pageRect.midX - footerSize.width / 2,
            y: pageRect.maxY - 30,
            width: footerSize.width,
            height: footerSize.height
        )
        
        footerText.draw(in: footerRect, withAttributes: attributes)
    }
}
