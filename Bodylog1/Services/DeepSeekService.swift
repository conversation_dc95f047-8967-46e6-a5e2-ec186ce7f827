//
//  DeepSeekService.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/20.
//

import Foundation

/// DeepSeek API调用服务
class DeepSeekService {
    
    /// 单例实例
    static let shared = DeepSeekService()
    
    private init() {}
    
    /// API错误类型
    enum APIError: Error, LocalizedError {
        case invalidAPIKey
        case invalidURL
        case noData
        case decodingError
        case networkError(String)
        case apiError(String)
        
        var errorDescription: String? {
            switch self {
            case .invalidAPIKey:
                return "API密钥无效"
            case .invalidURL:
                return "API URL无效"
            case .noData:
                return "服务器未返回数据"
            case .decodingError:
                return "数据解析失败"
            case .networkError(let message):
                return "网络错误: \(message)"
            case .apiError(let message):
                return "API错误: \(message)"
            }
        }
    }
    
    /// API请求结构
    private struct ChatRequest: Codable {
        let model: String
        let messages: [Message]
        let temperature: Double
        let max_tokens: Int
        
        struct Message: Codable {
            let role: String
            let content: String
        }
    }
    
    /// API响应结构
    private struct ChatResponse: Codable {
        let choices: [Choice]
        let usage: Usage?
        
        struct Choice: Codable {
            let message: Message
            let finish_reason: String?
            
            struct Message: Codable {
                let role: String
                let content: String
            }
        }
        
        struct Usage: Codable {
            let prompt_tokens: Int
            let completion_tokens: Int
            let total_tokens: Int
        }
    }
    
    /// 生成分析报告
    /// - Parameter prompt: 包含用户信息和记录的完整prompt
    /// - Returns: AI生成的分析报告内容
    func generateAnalysisReport(prompt: String) async throws -> String {
        // 检查API密钥
        guard ConfigManager.shared.isAPIKeyValid else {
            throw APIError.invalidAPIKey
        }
        
        // 构建请求URL
        let baseURL = ConfigManager.shared.deepSeekBaseURL
        guard let url = URL(string: "\(baseURL)/chat/completions") else {
            throw APIError.invalidURL
        }
        
        // 构建请求体
        let request = ChatRequest(
            model: "deepseek-reasoner", // DeepSeek R1模型
            messages: [
                ChatRequest.Message(role: "user", content: prompt)
            ],
            temperature: 0.7,
            max_tokens: 4000
        )
        
        // 创建URLRequest
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.setValue("Bearer \(ConfigManager.shared.deepSeekAPIKey)", forHTTPHeaderField: "Authorization")
        // 设置超时时间为60秒
        urlRequest.timeoutInterval = 180.0

        do {
            urlRequest.httpBody = try JSONEncoder().encode(request)
        } catch {
            throw APIError.decodingError
        }

        // 创建自定义URLSession配置
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 180.0  // 请求超时60秒
        config.timeoutIntervalForResource = 300.0 // 资源超时120秒
        let session = URLSession(configuration: config)

        // 发送请求
        do {
            let (data, response) = try await session.data(for: urlRequest)
            
            // 检查HTTP状态码
            if let httpResponse = response as? HTTPURLResponse {
                guard 200...299 ~= httpResponse.statusCode else {
                    let errorMessage = String(data: data, encoding: .utf8) ?? "未知错误"
                    throw APIError.apiError("HTTP \(httpResponse.statusCode): \(errorMessage)")
                }
            }
            
            // 解析响应
            let chatResponse = try JSONDecoder().decode(ChatResponse.self, from: data)
            
            guard let firstChoice = chatResponse.choices.first else {
                throw APIError.noData
            }
            
            return firstChoice.message.content
            
        } catch let error as APIError {
            throw error
        } catch {
            // 特殊处理超时错误
            if let urlError = error as? URLError {
                switch urlError.code {
                case .timedOut:
                    throw APIError.networkError("请求超时，请检查网络连接后重试")
                case .notConnectedToInternet:
                    throw APIError.networkError("网络连接不可用，请检查网络设置")
                case .networkConnectionLost:
                    throw APIError.networkError("网络连接中断，请重试")
                default:
                    throw APIError.networkError("网络错误: \(urlError.localizedDescription)")
                }
            } else {
                throw APIError.networkError(error.localizedDescription)
            }
        }
    }

    /// 生成身体档案
    /// - Parameter prompt: 包含用户信息和记录的完整prompt
    /// - Returns: AI生成的身体档案内容
    func generateBodyArchive(prompt: String) async throws -> String {
        // 复用分析报告的API调用逻辑，因为它们使用相同的API端点
        return try await generateAnalysisReport(prompt: prompt)
    }
}
