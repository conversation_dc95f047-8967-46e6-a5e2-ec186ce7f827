//
//  UserAgreementManager.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/21.
//

import Foundation

/// 用户协议同意状态管理器
/// 负责管理用户是否已同意用户协议和隐私政策的状态
class UserAgreementManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = UserAgreementManager()
    
    // MARK: - UserDefaults Keys
    private enum UserDefaultsKeys {
        static let hasAgreedToUserAgreement = "hasAgreedToUserAgreement"
        static let hasAgreedToPrivacyPolicy = "hasAgreedToPrivacyPolicy"
        static let agreementAcceptedDate = "agreementAcceptedDate"
        static let agreementVersion = "agreementVersion"
    }
    
    // MARK: - Constants
    private let currentAgreementVersion = "1.0.0" // 当前协议版本
    
    // MARK: - Published Properties
    @Published var hasAgreedToTerms: Bool = false
    @Published var needsToShowAgreement: Bool = false
    
    // MARK: - Private Properties
    private let userDefaults = UserDefaults.standard
    
    // MARK: - Initialization
    private init() {
        checkAgreementStatus()
    }
    
    // MARK: - Public Methods
    
    /// 检查用户是否需要显示协议弹窗
    /// - Returns: 如果需要显示协议弹窗返回true，否则返回false
    func shouldShowAgreementPopup() -> Bool {
        let hasAgreedToUserAgreement = userDefaults.bool(forKey: UserDefaultsKeys.hasAgreedToUserAgreement)
        let hasAgreedToPrivacyPolicy = userDefaults.bool(forKey: UserDefaultsKeys.hasAgreedToPrivacyPolicy)
        let savedVersion = userDefaults.string(forKey: UserDefaultsKeys.agreementVersion) ?? ""
        
        // 如果用户未同意任一协议，或协议版本已更新，则需要显示弹窗
        let needsToShow = !hasAgreedToUserAgreement || 
                         !hasAgreedToPrivacyPolicy || 
                         savedVersion != currentAgreementVersion
        
        return needsToShow
    }
    
    /// 用户同意协议
    func agreeToTerms() {
        userDefaults.set(true, forKey: UserDefaultsKeys.hasAgreedToUserAgreement)
        userDefaults.set(true, forKey: UserDefaultsKeys.hasAgreedToPrivacyPolicy)
        userDefaults.set(Date(), forKey: UserDefaultsKeys.agreementAcceptedDate)
        userDefaults.set(currentAgreementVersion, forKey: UserDefaultsKeys.agreementVersion)
        
        DispatchQueue.main.async {
            self.hasAgreedToTerms = true
            self.needsToShowAgreement = false
        }
        
        print("✅ 用户已同意用户协议和隐私政策")
    }
    
    /// 用户拒绝协议（退出应用）
    func disagreeToTerms() {
        print("❌ 用户拒绝协议，应用将退出")
        
        // 清除可能存在的部分同意状态
        userDefaults.removeObject(forKey: UserDefaultsKeys.hasAgreedToUserAgreement)
        userDefaults.removeObject(forKey: UserDefaultsKeys.hasAgreedToPrivacyPolicy)
        userDefaults.removeObject(forKey: UserDefaultsKeys.agreementAcceptedDate)
        userDefaults.removeObject(forKey: UserDefaultsKeys.agreementVersion)
        
        // 退出应用
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            exit(0)
        }
    }
    
    /// 重置协议同意状态（用于测试或重新显示协议）
    func resetAgreementStatus() {
        userDefaults.removeObject(forKey: UserDefaultsKeys.hasAgreedToUserAgreement)
        userDefaults.removeObject(forKey: UserDefaultsKeys.hasAgreedToPrivacyPolicy)
        userDefaults.removeObject(forKey: UserDefaultsKeys.agreementAcceptedDate)
        userDefaults.removeObject(forKey: UserDefaultsKeys.agreementVersion)
        
        DispatchQueue.main.async {
            self.hasAgreedToTerms = false
            self.needsToShowAgreement = true
        }
        
        print("🔄 协议同意状态已重置")
    }
    
    /// 获取协议同意日期
    /// - Returns: 用户同意协议的日期，如果未同意则返回nil
    func getAgreementAcceptedDate() -> Date? {
        return userDefaults.object(forKey: UserDefaultsKeys.agreementAcceptedDate) as? Date
    }
    
    /// 获取当前协议版本
    /// - Returns: 当前协议版本号
    func getCurrentAgreementVersion() -> String {
        return currentAgreementVersion
    }
    
    /// 获取用户同意的协议版本
    /// - Returns: 用户同意的协议版本号，如果未同意则返回nil
    func getAgreedAgreementVersion() -> String? {
        return userDefaults.string(forKey: UserDefaultsKeys.agreementVersion)
    }
    
    // MARK: - Private Methods
    
    /// 检查协议状态并更新发布的属性
    private func checkAgreementStatus() {
        let shouldShow = shouldShowAgreementPopup()
        
        DispatchQueue.main.async {
            self.hasAgreedToTerms = !shouldShow
            self.needsToShowAgreement = shouldShow
        }
    }
    
    /// 检查是否为首次启动
    /// - Returns: 如果是首次启动返回true，否则返回false
    func isFirstLaunch() -> Bool {
        let hasLaunchedBefore = userDefaults.bool(forKey: "hasLaunchedBefore")
        if !hasLaunchedBefore {
            userDefaults.set(true, forKey: "hasLaunchedBefore")
            return true
        }
        return false
    }
}

// MARK: - Debug Extensions
#if DEBUG
extension UserAgreementManager {
    
    /// 调试信息
    func printDebugInfo() {
        print("=== UserAgreementManager Debug Info ===")
        print("Has agreed to user agreement: \(userDefaults.bool(forKey: UserDefaultsKeys.hasAgreedToUserAgreement))")
        print("Has agreed to privacy policy: \(userDefaults.bool(forKey: UserDefaultsKeys.hasAgreedToPrivacyPolicy))")
        print("Agreement version: \(userDefaults.string(forKey: UserDefaultsKeys.agreementVersion) ?? "None")")
        print("Current version: \(currentAgreementVersion)")
        print("Should show popup: \(shouldShowAgreementPopup())")
        print("Agreement accepted date: \(getAgreementAcceptedDate()?.description ?? "None")")
        print("=====================================")
    }
}
#endif
