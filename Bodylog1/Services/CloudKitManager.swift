//
//  CloudKitManager.swift
//  Bodylog1
//
//  Created by rainkygong on 2025/7/21.
//

import Foundation
import CloudKit
import Network
import Combine

/// CloudKit管理器 - 处理iCloud同步状态和错误
@MainActor
class CloudKitManager: ObservableObject {
    static let shared = CloudKitManager()

    // MARK: - Published Properties
    @Published var accountStatus: CKAccountStatus = .couldNotDetermine
    @Published var syncStatus: SyncStatus = .unknown
    @Published var isNetworkAvailable: Bool = true
    @Published var lastSyncDate: Date?
    @Published var errorMessage: String?
    @Published var retryCount: Int = 0

    // MARK: - Private Properties
    private let container: CKContainer
    private let database: CKDatabase
    private let networkMonitor = NWPathMonitor()
    private let networkQueue = DispatchQueue(label: "NetworkMonitor")
    private var cancellables = Set<AnyCancellable>()
    private let errorHandler = SyncErrorHandler.shared
    private let maxRetryAttempts = 3

    // MARK: - Sync Status Enum
    enum SyncStatus: Equatable {
        case unknown
        case syncing
        case synced
        case failed(Error)
        case noAccount
        case networkUnavailable

        var description: String {
            switch self {
            case .unknown:
                return "未知状态"
            case .syncing:
                return "正在同步..."
            case .synced:
                return "已同步"
            case .failed(let error):
                return "同步失败: \(error.localizedDescription)"
            case .noAccount:
                return "未登录iCloud"
            case .networkUnavailable:
                return "网络不可用"
            }
        }

        static func == (lhs: SyncStatus, rhs: SyncStatus) -> Bool {
            switch (lhs, rhs) {
            case (.unknown, .unknown),
                 (.syncing, .syncing),
                 (.synced, .synced),
                 (.noAccount, .noAccount),
                 (.networkUnavailable, .networkUnavailable):
                return true
            case (.failed(let lhsError), .failed(let rhsError)):
                return lhsError.localizedDescription == rhsError.localizedDescription
            default:
                return false
            }
        }

        var isError: Bool {
            switch self {
            case .failed, .noAccount, .networkUnavailable:
                return true
            default:
                return false
            }
        }
    }

    // MARK: - Initialization
    private init() {
        self.container = CKContainer(identifier: "iCloud.com.rainkygong.Bodylog1")
        self.database = container.privateCloudDatabase

        // 检查是否在预览环境中
        if !ProcessInfo.processInfo.environment.keys.contains("XCODE_RUNNING_FOR_PREVIEWS") {
            setupNetworkMonitoring()
            checkAccountStatus()
        } else {
            // 预览环境中设置默认状态
            self.accountStatus = .available
            self.syncStatus = .synced
            self.isNetworkAvailable = true
        }
    }

    // MARK: - Public Methods

    /// 检查iCloud账户状态
    func checkAccountStatus() {
        Task {
            do {
                let status = try await container.accountStatus()
                await MainActor.run {
                    self.accountStatus = status
                    self.updateSyncStatus()
                }
            } catch {
                await MainActor.run {
                    self.accountStatus = .couldNotDetermine
                    let syncError = self.errorHandler.handleCloudKitError(error)
                    self.syncStatus = .failed(syncError)
                    self.errorMessage = syncError.errorDescription

                    // 如果是可重试的错误，安排重试
                    if self.errorHandler.isRetryableError(syncError) && self.retryCount < self.maxRetryAttempts {
                        self.scheduleRetry(for: syncError)
                    }
                }
            }
        }
    }

    /// 手动触发同步
    func triggerSync() {
        guard accountStatus == .available else {
            syncStatus = .noAccount
            return
        }

        guard isNetworkAvailable else {
            syncStatus = .networkUnavailable
            return
        }

        syncStatus = .syncing

        // 这里可以添加具体的同步逻辑
        // 由于Core Data + CloudKit会自动同步，我们主要是更新状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            self.syncStatus = .synced
            self.lastSyncDate = Date()
        }
    }

    /// 清除错误消息
    func clearError() {
        errorMessage = nil
        retryCount = 0
        if syncStatus.isError {
            syncStatus = .unknown
        }
    }

    /// 强制重新同步
    func forceSync() {
        retryCount = 0
        triggerSync()
    }
}

// MARK: - Private Methods
private extension CloudKitManager {

    /// 设置网络监控
    func setupNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.isNetworkAvailable = path.status == .satisfied
                self?.updateSyncStatus()
            }
        }
        networkMonitor.start(queue: networkQueue)
    }

    /// 更新同步状态
    func updateSyncStatus() {
        if accountStatus != .available {
            syncStatus = .noAccount
        } else if !isNetworkAvailable {
            syncStatus = .networkUnavailable
        } else if syncStatus == .noAccount || syncStatus == .networkUnavailable {
            syncStatus = .unknown
        }
    }

    /// 安排重试
    private func scheduleRetry(for error: SyncErrorHandler.SyncError) {
        retryCount += 1
        let delay = errorHandler.getRetryDelay(for: error, attempt: retryCount)

        DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
            print("重试同步，第 \(self.retryCount) 次尝试")
            self.checkAccountStatus()
        }
    }
}