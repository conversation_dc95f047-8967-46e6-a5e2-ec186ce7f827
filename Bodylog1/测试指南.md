# 身体日记功能测试指南

## 🎯 新增功能概述

本次更新添加了两个重要功能：
1. **坚持记录天数统计功能** - 统计用户有历史记录的天数总和
2. **语音转文字功能** - 使用iOS Speech框架实现实时语音识别

## 📱 测试环境要求

- **iOS版本**: 16.6 或更高版本
- **设备要求**: 真机测试（模拟器无法测试语音功能）
- **权限要求**: 麦克风权限、语音识别权限

## 🧪 功能测试步骤

### 1. 坚持记录天数统计功能测试

#### 测试步骤：
1. 打开应用，进入"记录"页面
2. 查看页面底部的坚持天数显示
3. 添加一条新记录
4. 观察坚持天数是否正确更新
5. 在不同日期添加多条记录
6. 验证统计的是不重复日期数量，而非记录总数

#### 预期结果：
- 初次使用时显示"你已经坚持记录了 0 天"
- 添加记录后，天数会根据有记录的不重复日期数量更新
- 同一天添加多条记录，天数不会重复计算

### 2. 语音转文字功能测试

#### 权限测试：
1. 首次打开记录页面时，应该自动请求权限
2. 如果权限被拒绝，输入框下方会显示权限提示信息
3. 可以在系统设置中重新授权

#### 语音识别测试：
1. 确保已授权麦克风和语音识别权限
2. 进入"记录"页面
3. 长按底部TabBar中间的绿色录音按钮
4. 开始说话（建议说中文）
5. 观察按钮变为红色，表示正在录音
6. 松开按钮停止录音
7. 查看输入框中是否出现识别的文字

#### 预期结果：
- 按住录音按钮时，按钮变为红色
- 实时显示语音识别结果在输入框中
- 松开按钮后停止录音
- 识别的文字可以继续编辑

## 🔧 测试场景

### 场景1：首次使用
1. 全新安装应用
2. 进入记录页面
3. 验证权限请求流程
4. 测试语音录音功能

### 场景2：权限被拒绝
1. 在系统设置中拒绝麦克风或语音识别权限
2. 打开应用，观察权限提示
3. 尝试录音，验证错误处理

### 场景3：多天记录
1. 在不同日期添加记录
2. 验证天数统计的准确性
3. 测试语音录音和手动输入混合使用

### 场景4：网络环境
1. 在有网络环境下测试语音识别
2. 在无网络环境下测试（语音识别可能需要网络）

## ⚠️ 注意事项

### 语音识别限制：
- 需要安静的环境以获得最佳识别效果
- 语音识别服务可能需要网络连接
- 识别准确度取决于发音清晰度和环境噪音

### 权限处理：
- 如果权限被拒绝，需要引导用户到系统设置中手动开启
- 权限状态会实时更新并显示在界面上

### 兼容性：
- 确保在iOS 16.6+系统上测试
- 不同设备的语音识别效果可能有差异

## 🐛 常见问题排查

### 问题1：语音识别不工作
**可能原因：**
- 权限未授权
- 设备不支持语音识别
- 网络连接问题

**解决方案：**
- 检查系统设置中的权限状态
- 确保设备支持Siri和语音识别
- 检查网络连接

### 问题2：天数统计不准确
**可能原因：**
- 数据同步问题
- 时区设置问题

**解决方案：**
- 重启应用刷新数据
- 检查设备时区设置

### 问题3：录音按钮无响应
**可能原因：**
- 权限问题
- 音频会话冲突

**解决方案：**
- 重新授权权限
- 关闭其他音频应用
- 重启应用

## 📋 测试检查清单

- [ ] 坚持天数显示正确
- [ ] 添加记录后天数更新
- [ ] 语音识别权限请求正常
- [ ] 麦克风权限请求正常
- [ ] 长按录音按钮开始录音
- [ ] 松开按钮停止录音
- [ ] 语音转文字准确显示
- [ ] 权限被拒绝时显示提示
- [ ] 错误处理正常工作
- [ ] 在不同iOS版本上测试

## 🚀 性能优化建议

1. **语音识别优化：**
   - 在安静环境下测试
   - 说话清晰、语速适中
   - 避免方言和口音过重

2. **用户体验优化：**
   - 首次使用时提供操作指导
   - 权限被拒绝时提供明确的解决方案
   - 添加语音识别状态的视觉反馈

3. **数据准确性：**
   - 定期验证天数统计的准确性
   - 确保数据同步正常工作
