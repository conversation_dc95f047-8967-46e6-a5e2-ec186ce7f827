//
//  NotificationManager.swift
//  Bodylog1
//
//  Created by rainkygong on 2025/7/21.
//

import Foundation
import UserNotifications
import UIKit

/// 通知管理器，负责处理本地通知的权限、调度和管理
@MainActor
class NotificationManager: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    @Published var authorizationStatus: UNAuthorizationStatus = .notDetermined
    @Published var isNotificationEnabled = false
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private let notificationCenter = UNUserNotificationCenter.current()
    
    // MARK: - Initialization
    override init() {
        super.init()
        notificationCenter.delegate = self

        // 检查是否在预览环境中
        if !ProcessInfo.processInfo.environment.keys.contains("XCODE_RUNNING_FOR_PREVIEWS") {
            checkAuthorizationStatus()
        }
    }
    
    // MARK: - Authorization Management
    
    /// 检查当前通知权限状态
    func checkAuthorizationStatus() {
        Task {
            let settings = await notificationCenter.notificationSettings()
            await MainActor.run {
                self.authorizationStatus = settings.authorizationStatus
                self.isNotificationEnabled = settings.authorizationStatus == .authorized
            }
        }
    }
    
    /// 请求通知权限
    func requestAuthorization() async -> Bool {
        do {
            let granted = try await notificationCenter.requestAuthorization(
                options: [.alert, .sound, .badge]
            )
            
            await MainActor.run {
                self.isNotificationEnabled = granted
                self.authorizationStatus = granted ? .authorized : .denied
                self.errorMessage = nil
            }
            
            return granted
        } catch {
            await MainActor.run {
                self.errorMessage = "请求通知权限失败: \(error.localizedDescription)"
                self.isNotificationEnabled = false
            }
            return false
        }
    }
    
    // MARK: - Notification Scheduling
    
    /// 为提醒创建本地通知
    /// - Parameter reminder: 提醒实体
    func scheduleNotification(for reminder: ReminderEntity) async {
        guard isNotificationEnabled else {
            await MainActor.run {
                self.errorMessage = "通知权限未授权，请先开启通知权限"
            }
            return
        }
        
        guard let time = reminder.time,
              let message = reminder.message,
              let _ = reminder.id else {
            await MainActor.run {
                self.errorMessage = "提醒数据不完整"
            }
            return
        }
        
        // 创建通知内容
        let content = UNMutableNotificationContent()
        content.title = "身体日记提醒"
        content.body = message
        content.sound = .default
        content.badge = 1
        
        // 创建时间触发器（每日重复）
        let calendar = Calendar.current
        let components = calendar.dateComponents([.hour, .minute], from: time)
        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: true)
        
        // 创建通知请求
        let identifier = reminder.notificationIdentifier
        let request = UNNotificationRequest(
            identifier: identifier,
            content: content,
            trigger: trigger
        )
        
        do {
            try await notificationCenter.add(request)
            print("✅ 成功调度提醒通知: \(identifier)")
        } catch {
            await MainActor.run {
                self.errorMessage = "调度通知失败: \(error.localizedDescription)"
            }
        }
    }
    
    /// 取消指定提醒的通知
    /// - Parameter reminder: 提醒实体
    func cancelNotification(for reminder: ReminderEntity) {
        let identifier = reminder.notificationIdentifier
        notificationCenter.removePendingNotificationRequests(withIdentifiers: [identifier])
        print("🗑️ 已取消提醒通知: \(identifier)")
    }
    
    /// 更新提醒通知状态
    /// - Parameter reminder: 提醒实体
    func updateNotification(for reminder: ReminderEntity) async {
        // 先取消现有通知
        cancelNotification(for: reminder)
        
        // 如果提醒是激活状态，重新调度
        if reminder.isActive {
            await scheduleNotification(for: reminder)
        }
    }
    
    /// 取消所有待处理的通知
    func cancelAllNotifications() {
        notificationCenter.removeAllPendingNotificationRequests()
        print("🗑️ 已取消所有待处理的通知")
    }
    
    /// 获取所有待处理的通知
    func getPendingNotifications() async -> [UNNotificationRequest] {
        return await notificationCenter.pendingNotificationRequests()
    }
    
    // MARK: - Utility Methods
    
    /// 检查是否可以发送通知
    var canSendNotifications: Bool {
        return authorizationStatus == .authorized
    }
    
    /// 获取权限状态描述
    var authorizationStatusDescription: String {
        switch authorizationStatus {
        case .notDetermined:
            return "未确定"
        case .denied:
            return "已拒绝"
        case .authorized:
            return "已授权"
        case .provisional:
            return "临时授权"
        case .ephemeral:
            return "临时授权"
        @unknown default:
            return "未知状态"
        }
    }
    
    /// 打开系统设置页面
    func openSettings() {
        guard let settingsUrl = URL(string: UIApplication.openSettingsURLString) else {
            return
        }
        
        if UIApplication.shared.canOpenURL(settingsUrl) {
            UIApplication.shared.open(settingsUrl)
        }
    }
}

// MARK: - UNUserNotificationCenterDelegate
extension NotificationManager: UNUserNotificationCenterDelegate {

    /// 应用在前台时收到通知的处理
    nonisolated func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // 在前台也显示通知
        completionHandler([.banner, .sound, .badge])
    }

    /// 用户点击通知的处理
    nonisolated func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        // 处理用户点击通知的逻辑
        print("用户点击了通知: \(response.notification.request.identifier)")

        // 可以在这里添加跳转到特定页面的逻辑
        // 例如：跳转到记录页面

        completionHandler()
    }
}
