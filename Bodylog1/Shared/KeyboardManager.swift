//
//  KeyboardManager.swift
//  Bodylog1
//
//  Created by rainkygong on 2025/7/20.
//

import SwiftUI
import UIKit

// MARK: - 键盘管理器
class KeyboardManager: ObservableObject {
    @Published var isKeyboardVisible = false
    @Published var keyboardHeight: CGFloat = 0
    
    init() {
        setupKeyboardNotifications()
    }
    
    private func setupKeyboardNotifications() {
        NotificationCenter.default.addObserver(
            forName: UIResponder.keyboardWillShowNotification,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            self?.handleKeyboardWillShow(notification)
        }
        
        NotificationCenter.default.addObserver(
            forName: UIResponder.keyboardWillHideNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleKeyboardWillHide()
        }
    }
    
    private func handleKeyboardWillShow(_ notification: Notification) {
        guard let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? NSValue else {
            return
        }

        let keyboardRect = keyboardFrame.cgRectValue
        DispatchQueue.main.async {
            self.keyboardHeight = keyboardRect.height
            self.isKeyboardVisible = true
        }
    }

    private func handleKeyboardWillHide() {
        DispatchQueue.main.async {
            self.keyboardHeight = 0
            self.isKeyboardVisible = false
        }
    }
    
    func dismissKeyboard() {
        UIApplication.shared.sendAction(
            #selector(UIResponder.resignFirstResponder),
            to: nil,
            from: nil,
            for: nil
        )
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - 键盘收回按钮
struct KeyboardDismissButton: View {
    @ObservedObject var keyboardManager: KeyboardManager
    
    var body: some View {
        if keyboardManager.isKeyboardVisible {
            VStack {
                Spacer()
                HStack {
                    Spacer()
                    Button(action: {
                        keyboardManager.dismissKeyboard()
                    }) {
                        Image("键盘")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 24, height: 24)
                            .padding(12)
                            .background(
                                Circle()
                                    .fill(.ultraThinMaterial)
                                    .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                            )
                    }
                    .padding(.trailing, 16)
                    .padding(.bottom, keyboardManager.keyboardHeight + 16)
                }
            }
            .transition(.asymmetric(
                insertion: .scale(scale: 0.8).combined(with: .opacity),
                removal: .scale(scale: 0.8).combined(with: .opacity)
            ))
            .animation(.spring(response: 0.3, dampingFraction: 0.8), value: keyboardManager.isKeyboardVisible)
        }
    }
}

// MARK: - 键盘适配修饰符
struct KeyboardAdaptive: ViewModifier {
    @ObservedObject var keyboardManager: KeyboardManager
    
    func body(content: Content) -> some View {
        content
            .ignoresSafeArea(.keyboard, edges: .bottom)
            .overlay(
                KeyboardDismissButton(keyboardManager: keyboardManager),
                alignment: .bottomTrailing
            )
    }
}

extension View {
    func keyboardAdaptive(keyboardManager: KeyboardManager) -> some View {
        modifier(KeyboardAdaptive(keyboardManager: keyboardManager))
    }
}
