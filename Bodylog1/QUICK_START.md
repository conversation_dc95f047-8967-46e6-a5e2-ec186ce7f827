# 身体日记 - 快速开始指南

## 🎯 当前状态：✅ 编译成功

所有 CoreData 实体已创建完成，编译错误已修复，可以立即使用！

## 🚀 3 步快速集成

### 步骤 1：更新 App 入口文件

在 `Bodylog1App.swift` 中添加一行代码：

```swift
@main
struct Bodylog1App: App {
    let persistenceController = PersistenceController.shared

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.managedObjectContext, persistenceController.container.viewContext)
                .environmentObject(DataViewModel()) // 👈 添加这行
        }
    }
}
```

### 步骤 2：在视图中使用数据

在任何需要使用数据的视图中添加：

```swift
struct YourView: View {
    @EnvironmentObject var dataViewModel: DataViewModel // 👈 添加这行
    
    var body: some View {
        // 你的 UI 代码
    }
}
```

### 步骤 3：开始使用数据操作

```swift
// 添加记录
Button("保存记录") {
    Task {
        await dataViewModel.addRecord(text: recordText, timestamp: Date())
    }
}

// 获取今天的记录
let todayRecords = dataViewModel.getRecords(for: Date())

// 显示记录
ForEach(todayRecords, id: \.objectID) { record in
    Text(record.text ?? "")
}
```

## 📱 核心功能

### 记录管理
```swift
// 添加记录
await dataViewModel.addRecord(text: "今天头痛")

// 获取指定日期记录
let records = dataViewModel.getRecords(for: Date())

// 删除记录
await dataViewModel.deleteRecord(record)
```

### 用户信息
```swift
// 获取用户信息
if let userInfo = dataViewModel.userInfo {
    Text("剩余调用: \(userInfo.remainingCalls)")
}

// 消费调用次数
let success = await dataViewModel.consumeApiCall()

// 增加调用次数（内购后）
await dataViewModel.addApiCalls(100)
```

### 报告生成
```swift
// 生成分析报告
await dataViewModel.addReport(
    title: "本周健康分析",
    type: .analysis,
    content: "AI 生成的内容...",
    timeRange: "2025年7月14日-20日"
)
```

### 提醒管理
```swift
// 添加提醒
await dataViewModel.addReminder(
    time: reminderTime,
    message: "记录身体状态"
)

// 获取激活的提醒
let activeReminders = dataViewModel.getActiveReminders()
```

## 🎨 UI 组件

项目提供了现成的 UI 组件：

```swift
// 统计卡片
StatisticsCard(
    totalRecords: stats.totalRecords,
    totalReports: stats.totalReports,
    activeReminders: stats.activeReminders
)

// 用户信息卡片
UserInfoCard(userInfo: userInfo) {
    // 购买更多调用次数
    Task {
        await dataViewModel.addApiCalls(100)
    }
}

// 记录卡片
RecordCard(record: record) {
    // 删除记录
    Task {
        await dataViewModel.deleteRecord(record)
    }
}
```

## 🔄 替换现有示例数据

### 在历史记录页面中：

```swift
// 替换这个：
// @State private var historyRecords: [HistoryRecord] = [...]

// 使用这个：
var todayRecords: [RecordEntity] {
    dataViewModel.getRecords(for: selectedDate)
}

// 替换这个：
// let recordDates: Set<Date> = [...]

// 使用这个：
var recordDates: Set<Date> {
    dataViewModel.recordDates
}
```

### 在记录页面中：

```swift
// 保存记录的方法
private func saveRecord() {
    let text = recordText.trimmingCharacters(in: .whitespacesAndNewlines)
    guard !text.isEmpty else { return }
    
    Task {
        await dataViewModel.addRecord(text: text, timestamp: Date())
        recordText = "" // 清空输入框
    }
}
```

### 在个人中心页面中：

```swift
// 显示用户信息
if let userInfo = dataViewModel.userInfo {
    Text("剩余调用次数: \(userInfo.remainingCalls)")
    Text("性别: \(userInfo.userGender?.displayName ?? "未设置")")
    Text("年龄: \(userInfo.age)岁")
}

// 显示统计信息
let stats = dataViewModel.getStatistics()
Text("总记录: \(stats.totalRecords)")
Text("报告数: \(stats.totalReports)")
Text("提醒数: \(stats.activeReminders)")
```

## ☁️ CloudKit 同步

数据会自动同步到 iCloud，支持：
- ✅ 多设备数据同步
- ✅ 离线数据访问
- ✅ 自动冲突解决

**注意：** 用户需要登录 iCloud 账户才能同步。

## 🧪 测试验证

运行测试确保一切正常：

```swift
// 在开发时运行测试
DataModelTests.testEntityCreation()

// 或者使用测试视图
NavigationLink("测试数据模型") {
    DataModelTestView()
}
```

## ⚠️ 重要提醒

1. **首次使用** - 用户信息会自动创建，默认 20 次 API 调用
2. **数据验证** - 所有实体都有 `isValid` 属性进行数据验证
3. **异步操作** - 所有数据操作都是异步的，使用 `await`
4. **错误处理** - 检查 `dataViewModel.errorMessage` 显示错误信息

## 📞 常见问题

**Q: 数据不显示？**
A: 确保在视图的 `onAppear` 中调用 `await dataViewModel.refreshData()`

**Q: 调用次数不足？**
A: 检查 `userInfo.remainingCalls`，实现内购逻辑增加调用次数

**Q: 数据不同步？**
A: 检查网络连接和 iCloud 登录状态

## 🎉 完成！

现在你的身体日记应用已经完全集成了 CoreData 数据模型！

- ✅ 零编译错误
- ✅ 完整功能
- ✅ CloudKit 同步
- ✅ 现成的 UI 组件

开始享受你的新数据模型吧！🚀
